import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect, useSelector } from 'react-redux';
import { withRouter } from 'next/router';
import Head from 'next/head';
import { setCachedRoute } from 'reducer/auth';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { getUserData } from 'reducer/user';
import {
  getCallOutList,
  getMyCallOutList,
  getCalloutSubmissions,
} from 'reducer/callout';
import NewSidebar from 'sharedComponents/NewSidebar/newsidebar';
import Router from 'next/router';
import { withSubscriptionBanner } from 'sharedComponents/Subscription';
import MyCallOutSection from './calloutionSection/myCalloutSection';

const Index = ({
  userData,
  isLoading,
  getUserData,
  getCallOutList,
  callOutList,
  myCallOutList,
  getMyCallOutList,
  getCalloutSubmissions,
  setCachedRoute,
  router,
  token,
  bannerWrapper: BannerWrapper,
  bannerProps,
}) => {
  const [showOnlySubmissions, setShowOnlySubmissions] = React.useState(false);

  const handleViewMore = () => {
    setShowOnlySubmissions(true);
  };

  const handleBack = () => {
    setShowOnlySubmissions(false);
    // Only update URL if we currently have the expanded parameter
    if (router.query.expanded === 'true') {
      router.push('/callouts', undefined, { shallow: true });
    }
  };

  // Check URL parameters on component mount and route changes
  useEffect(() => {
    const { expanded } = router.query;
    if (expanded === 'true') {
      setShowOnlySubmissions(true);
    } else {
      setShowOnlySubmissions(false);
    }
  }, [router.query]);

  // Separate useEffect for scrolling to ensure it happens after data is loaded and DOM is rendered
  useEffect(() => {
    const { scrollTo, expanded } = router.query;

    // Only scroll if scrollTo parameter exists and we're not in expanded mode
    if (scrollTo && !expanded && !isLoading) {
      // Function to attempt scrolling with retries
      const attemptScroll = (attempts = 0) => {
        const targetElement = document.getElementById(scrollTo);

        if (targetElement) {
          // Add a small offset to account for any fixed headers
          const elementPosition =
            targetElement.getBoundingClientRect().top + window.pageYOffset;
          const offsetPosition = elementPosition - 20; // 20px offset from top

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth',
          });
        } else if (attempts < 10) {
          // Retry up to 10 times with increasing delays
          setTimeout(() => attemptScroll(attempts + 1), 100 * (attempts + 1));
        } else {
          console.log(
            `Failed to find element with ID: ${scrollTo} after ${attempts + 1} attempts`,
          );
        }
      };

      // Start scrolling attempt after a short delay
      setTimeout(() => attemptScroll(), 200);
    }
  }, [router.query, isLoading, callOutList, myCallOutList]);

  useEffect(() => {
    if (!token) {
      setCachedRoute('/callouts');
      router.push('/');
    } else {
      setCachedRoute('');
    }
    getUserData();
    getCallOutList({ isPublished: true });
    getMyCallOutList();
  }, [
    getUserData,
    getCallOutList,
    getMyCallOutList,
    router,
    setCachedRoute,
    token,
  ]);

  // Single useEffect to handle submissions fetching when userData changes
  useEffect(() => {
    if (userData && userData._id) {
      // Only fetch submissions once when we have a valid user ID
      getCalloutSubmissions({
        userId: userData._id,
        action: 'both',
        limit: 100, // This is per page, we'll fetch all pages
        sort: 'updatedAt|-1',
      });
    }
  }, [userData, getCalloutSubmissions]);

  const handleCreateCallout = () => {
    Router.push('/profile/createCallout?source=createCallout');
  };

  const calloutSubmissions = useSelector(
    (state) => state.callout.calloutSubmissions,
  );

  // No logging needed

  // More robust check for submissions
  const hasSubmission = React.useMemo(() => {
    // Handle paginated response format
    if (
      calloutSubmissions &&
      calloutSubmissions.docs &&
      Array.isArray(calloutSubmissions.docs)
    ) {
      // Check if we have submissions in the docs array
      const hasDocsWithSubmissions = calloutSubmissions.docs.length > 0;

      // Check if we have a total count greater than 0
      const hasTotalSubmissions = calloutSubmissions.total > 0;

      // Return true if either condition is met
      return hasDocsWithSubmissions || hasTotalSubmissions;
    }

    // Handle direct array format
    if (Array.isArray(calloutSubmissions)) {
      return calloutSubmissions.length > 0;
    }

    // Handle other formats
    return false;
  }, [calloutSubmissions]);

  // Force hasSubmission to true for testing
  // const hasSubmission = true;

  const publishedCallOutList = myCallOutList?.filter(
    (callout) => callout.isPublished,
  );

  return (
    <div className="themeContainer">
      <Head>
        <title>Callouts | Smash</title>
        <link rel="shortcut icon" href="/favicon.ico" />
      </Head>

      <div className="container-fluid">
        <div className="row">
          <div className="col-12 col-md-2 col-lg-2 p-0">
            <NewSidebar profile={userData.profile} />
          </div>

          <div className="page-wrapper col-12 col-md-10 col-lg-10">
            {BannerWrapper ? (
              <BannerWrapper bannerProps={bannerProps}>
                <MyCallOutSection
                  isLoading={isLoading}
                  showOnlySubmissions={showOnlySubmissions}
                  handleCreateCallout={handleCreateCallout}
                  hasSubmission={hasSubmission}
                  myCallOutList={myCallOutList}
                  userData={userData}
                  publishedCallOutList={publishedCallOutList}
                  callOutList={callOutList}
                  router={router}
                  onViewMore={handleViewMore}
                  onBack={handleBack}
                  expandSubmissions={router.query.expanded === 'true'}
                />
              </BannerWrapper>
            ) : (
              <MyCallOutSection
                isLoading={isLoading}
                showOnlySubmissions={showOnlySubmissions}
                handleCreateCallout={handleCreateCallout}
                hasSubmission={hasSubmission}
                myCallOutList={myCallOutList}
                userData={userData}
                publishedCallOutList={publishedCallOutList}
                callOutList={callOutList}
                router={router}
                onViewMore={handleViewMore}
                onBack={handleBack}
                expandSubmissions={router.query.expanded === 'true'}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

Index.propTypes = {
  userData: PropTypes.object.isRequired,
  isLoading: PropTypes.bool.isRequired,
  callOutList: PropTypes.array.isRequired,
  myCallOutList: PropTypes.array.isRequired,
  getUserData: PropTypes.func.isRequired,
  getCallOutList: PropTypes.func.isRequired,
  getMyCallOutList: PropTypes.func.isRequired,
  getCalloutSubmissions: PropTypes.func.isRequired,
  token: PropTypes.string.isRequired,
  setCachedRoute: PropTypes.func.isRequired,
  bannerWrapper: PropTypes.elementType,
  bannerProps: PropTypes.object,
};

export async function getStaticProps({ locale }) {
  return {
    props: await serverSideTranslations(locale),
  };
}

const mapStateToProps = (state) => ({
  userData: state.auth.userData,
  isLoading: state.project.isLoading,
  callOutList: state.callout.callOutList,
  myCallOutList: state.callout.myCallOutList,
  token: state.auth.token,
  accessibleFeature: state.subscription.accessibleFeature,
});

const mapDispatchToProps = (dispatch) => ({
  getUserData: (payload) => dispatch(getUserData(payload)),
  getCallOutList: (params) => dispatch(getCallOutList(params)),
  getMyCallOutList: () => dispatch(getMyCallOutList()),
  getCalloutSubmissions: (options) => dispatch(getCalloutSubmissions(options)),
  setCachedRoute: (route) => dispatch(setCachedRoute(route)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withRouter(withSubscriptionBanner(Index)));
