import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { withRouter } from 'next/router';
import Head from 'next/head';
import { setCachedRoute } from 'reducer/auth';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { getUserData } from 'reducer/user';
import {
  getCallOutList,
  getMyCallOutList,
  getCalloutSubmissions,
} from 'reducer/callout';
import {
  fetchDmProjects,
  getProjectList,
  deleteProject,
  fetchCollaboratorList,
  createCollaborator,
  updateOtherDocuments,
  sendReminderToCollaborator,
  fetchCollaborator,
  deleteCollaborator,
  removeSectionItems,
  softDeleteProject,
} from 'reducer/project';
import NewSidebar from 'sharedComponents/NewSidebar/newsidebar';
import { withSubscriptionBanner } from 'sharedComponents/Subscription';
import { DashboardContent } from '../../sharedComponents/MyDashboard';

const MyDashboard = ({
  userData,
  isLoading,
  getUserData,
  getCallOutList,
  callOutList,
  myCallOutList,
  getMyCallOutList,
  getCalloutSubmissions,
  fetchDmProjects,
  setCachedRoute,
  router,
  token,
  bannerWrapper: BannerWrapper,
  bannerProps,
  // Project-related props
  projectsList,
  // dmProjectsList,
  collaboratorList,
  getProjectList,
  deleteProject,
  fetchCollaboratorList,
  createCollaborator,
  updateOtherDocuments,
  sendReminderToCollaborator,
  fetchCollaborator,
  deleteCollaborator,
  removeSectionItems,
  softDeleteProject,
}) => {
  // Fetch data when component mounts
  useEffect(() => {
    if (!token) {
      setCachedRoute('/mydashboard');
      router.push('/');
    } else {
      setCachedRoute('');
    }
    getUserData();
    getCallOutList({ isPublished: true });
    getMyCallOutList();

    // Fetch DM projects for InterestedProjects component
    fetchDmProjects();

    // Fetch user's own projects for MyProjects component
    if (token) {
      const data = {
        params: { deleted: false },
      };
      getProjectList(data);
    }
  }, [
    getUserData,
    getCallOutList,
    getMyCallOutList,
    fetchDmProjects,
    getProjectList,
    router,
    setCachedRoute,
    token,
  ]);

  // Fetch submissions when userData changes
  useEffect(() => {
    if (userData && userData._id) {
      // Only fetch submissions once when we have a valid user ID
      getCalloutSubmissions({
        userId: userData._id,
        action: 'both',
        limit: 100, // This is per page, we'll fetch all pages
        sort: 'updatedAt|-1',
      });
    }
  }, [userData, getCalloutSubmissions]);

  return (
    <>
      <Head>
        <title>My Dashboard | Smash</title>
        <link rel="shortcut icon" href="/favicon.ico" />
      </Head>

      <div className="container-fluid">
        <div className="row">
          <div className="col-12 col-md-2 col-lg-2 p-0">
            <NewSidebar profile={userData.profile} />
          </div>
          <div className="page-wrapper col-12 col-md-10 col-lg-10">
            {BannerWrapper ? (
              <BannerWrapper bannerProps={bannerProps}>
                <div className="pb-5">
                  <DashboardContent
                    isLoading={isLoading}
                    myCallOutList={myCallOutList || []}
                    callOutList={callOutList || []}
                    welcomeTitle="welcome to Smash"
                    // Pass project-related props
                    projectsList={projectsList || []}
                    deleteProject={deleteProject}
                    collaboratorList={collaboratorList || []}
                    createCollaborator={createCollaborator}
                    fetchCollaboratorList={fetchCollaboratorList}
                    updateOtherDocuments={updateOtherDocuments}
                    sendReminderToCollaborator={sendReminderToCollaborator}
                    fetchCollaborator={fetchCollaborator}
                    deleteCollaborator={deleteCollaborator}
                    userData={userData}
                    removeSectionItems={removeSectionItems}
                    softDeleteProject={softDeleteProject}
                  />
                </div>
              </BannerWrapper>
            ) : (
              <div className="pb-5">
                <DashboardContent
                  isLoading={isLoading}
                  myCallOutList={myCallOutList || []}
                  callOutList={callOutList || []}
                  welcomeTitle="My Dashboard"
                  withButton={true}
                  // Pass project-related props
                  projectsList={projectsList || []}
                  deleteProject={deleteProject}
                  collaboratorList={collaboratorList || []}
                  createCollaborator={createCollaborator}
                  fetchCollaboratorList={fetchCollaboratorList}
                  updateOtherDocuments={updateOtherDocuments}
                  sendReminderToCollaborator={sendReminderToCollaborator}
                  fetchCollaborator={fetchCollaborator}
                  deleteCollaborator={deleteCollaborator}
                  userData={userData}
                  removeSectionItems={removeSectionItems}
                  softDeleteProject={softDeleteProject}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

MyDashboard.propTypes = {
  userData: PropTypes.object.isRequired,
  isLoading: PropTypes.bool.isRequired,
  callOutList: PropTypes.array.isRequired,
  myCallOutList: PropTypes.array.isRequired,
  getUserData: PropTypes.func.isRequired,
  getCallOutList: PropTypes.func.isRequired,
  getMyCallOutList: PropTypes.func.isRequired,
  getCalloutSubmissions: PropTypes.func.isRequired,
  fetchDmProjects: PropTypes.func.isRequired,
  token: PropTypes.string.isRequired,
  setCachedRoute: PropTypes.func.isRequired,
  bannerWrapper: PropTypes.elementType,
  bannerProps: PropTypes.object,
  // Project-related PropTypes
  projectsList: PropTypes.array,
  dmProjectsList: PropTypes.array,
  collaboratorList: PropTypes.array,
  getProjectList: PropTypes.func.isRequired,
  deleteProject: PropTypes.func.isRequired,
  fetchCollaboratorList: PropTypes.func.isRequired,
  createCollaborator: PropTypes.func.isRequired,
  updateOtherDocuments: PropTypes.func.isRequired,
  sendReminderToCollaborator: PropTypes.func.isRequired,
  fetchCollaborator: PropTypes.func.isRequired,
  deleteCollaborator: PropTypes.func.isRequired,
  removeSectionItems: PropTypes.func.isRequired,
  softDeleteProject: PropTypes.func.isRequired,
};

export async function getStaticProps({ locale }) {
  return {
    props: await serverSideTranslations(locale),
  };
}

const mapStateToProps = (state) => ({
  userData: state.auth.userData,
  isLoading: state.project.isLoading,
  callOutList: state.callout.callOutList,
  myCallOutList: state.callout.myCallOutList,
  token: state.auth.token,
  accessibleFeature: state.subscription.accessibleFeature,
  // Map project-related state
  projectsList: state.project.projectsList,
  dmProjectsList: state.project.dmProjectsList,
  collaboratorList: state.project.collaboratorList,
});

const mapDispatchToProps = (dispatch) => ({
  getUserData: (payload) => dispatch(getUserData(payload)),
  getCallOutList: (params) => dispatch(getCallOutList(params)),
  getMyCallOutList: () => dispatch(getMyCallOutList()),
  getCalloutSubmissions: (options) => dispatch(getCalloutSubmissions(options)),
  fetchDmProjects: (query) => dispatch(fetchDmProjects(query)),
  setCachedRoute: (route) => dispatch(setCachedRoute(route)),
  // Map project-related actions
  getProjectList: (payload) => dispatch(getProjectList(payload)),
  deleteProject: (payload) => dispatch(deleteProject(payload)),
  fetchCollaboratorList: (payload) => dispatch(fetchCollaboratorList(payload)),
  createCollaborator: (payload) => dispatch(createCollaborator(payload)),
  updateOtherDocuments: (value, id, section) =>
    dispatch(updateOtherDocuments(value, id, section)),
  sendReminderToCollaborator: (value, id) =>
    dispatch(sendReminderToCollaborator(value, id)),
  fetchCollaborator: (payload) => dispatch(fetchCollaborator(payload)),
  deleteCollaborator: (id) => dispatch(deleteCollaborator(id)),
  removeSectionItems: (projectId, section, sectionObjid) =>
    dispatch(removeSectionItems(projectId, section, sectionObjid)),
  softDeleteProject: (payload) => dispatch(softDeleteProject(payload)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withRouter(withSubscriptionBanner(MyDashboard)));
