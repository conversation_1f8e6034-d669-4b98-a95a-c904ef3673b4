/* eslint-disable @next/next/no-img-element */
import React from 'react';
import PropTypes from 'prop-types';
import { get } from 'lodash';
import { withTranslation } from 'react-i18next';
import Router from 'next/router';
import Style from 'pages/dashboard/styles/startApp.module.scss';
import Icon from 'sharedComponents/Icon/Icon';
import BackIconSvgPath from 'svgpath/BackIconSvgPath';
import Overview from '../dashboard/Overview';
import DeletedProject from './deletedItems';

function DeletedProjects(props) {
  const {
    projectsList,
    // t,
    deleteProject,
    updateOtherDocuments,
    userData,
    removeSectionItems,
    updateProjectData,
  } = props;

  const userId = get(userData, '_id');
  const list = projectsList || [];
  const showProject = list.map((item) => {
    return (
      <div
        className={`${Style.existingAppContainer} pt-5 p-md-5`}
        key={item._id}
      >
        <div>
          <div className={`${Style.existingAppSubContainer}`}>
            <DeletedProject
              item={item}
              deleteProject={deleteProject}
              updateOtherDocuments={updateOtherDocuments}
              userId={userId}
              removeSectionItems={removeSectionItems}
              updateProjectData={updateProjectData}
            />
            <Overview cover={item.cover} item={item} />
          </div>
        </div>
      </div>
    );
  });

  return (
    <div className="container-fluid position-relative">

      <div className="my-32 my-md-48 my-lg-48">
        {/* <div className=""> */}
        <div className="row align-items-center">
          <div className="col-md-4 d-none d-md-block text-start">
            <div
              style={{ cursor: 'pointer' }}
              onClick={() => Router.push('/dashboard')}
              aria-label="Back to Dashboard"
              role="button"
            >
              <Icon
                icon={BackIconSvgPath}
                color="#05012D"
                viewBox="24"
                iconSize="30px"
              />
            </div>
          </div>
          <div className="col-12 col-md-4 text-center mt-5 mt-md-0 mt-lg-0">
            <div className="d-flex d-md-none align-items-center justify-content-center mb-4 position-relative">
              <div
                style={{ cursor: 'pointer', position: 'absolute', left: '0' }}
                onClick={() => Router.push('/dashboard')}
                aria-label="Back to Dashboard"
                role="button"
              >
                <Icon
                  icon={BackIconSvgPath}
                  color="#05012D"
                  viewBox="20"
                  iconSize="24px"
                />
              </div>
              <h1 className="text-primary mb-0 fs-20">
                RECENTLY DELETED PROJECTS
              </h1>
            </div>
            <h1 className="text-primary mb-3 mb-md-0 fs-20 fs-md-24 fs-lg-24 d-none d-md-block">
              RECENTLY DELETED PROJECTS
            </h1>
          </div>
          <div className="col-md-4 d-none d-md-block"></div>
        </div>
        <div className="d-block">
          <p className="text-primary text-center p1 mt-3 mb-10 mb-md-34 mb-lg-34">
            These are the projects you deleted. They will remain here for 30 days before being permanently deleted.
          </p>
        </div>
        {/* <div className="d-block d-sm-block d-md-none">
            <p className="text-primary text-center p1 mb-10">
              These are the projects you deleted. They will remain here for 30 days before being permanently deleted.
            </p>
          </div> */}
        {list.length === 0 && (
          <div
            className="mt-54 text-primary text-center p1 mb-5 mx-auto d-flex justify-content-center align-items-center"
            style={{
              height: '348px',
            }}
          >
            <p>You have no deleted items</p>
          </div>
        )}
        {/* </div> */}

        <div className={Style.gridContainer}>
          {showProject}
        </div>
      </div>
    </div>
  );
}
DeletedProjects.defaultProps = {};

DeletedProjects.propTypes = {
  t: PropTypes.func.isRequired,
  token: PropTypes.string.isRequired,
  projectsList: PropTypes.array.isRequired,
  getProjectList: PropTypes.func.isRequired,
  deleteProject: PropTypes.func.isRequired,
  deleteCollaborator: PropTypes.func.isRequired,
  fetchCollaborator: PropTypes.func.isRequired,
  sendReminderToCollaborator: PropTypes.func.isRequired,
  updateOtherDocuments: PropTypes.func.isRequired,
  fetchCollaboratorList: PropTypes.func.isRequired,
  createCollaborator: PropTypes.func.isRequired,
  collaboratorList: PropTypes.array.isRequired,
  userData: PropTypes.object.isRequired,
  removeSectionItems: PropTypes.func.isRequired,
};

export default withTranslation('common')(DeletedProjects);
