/* eslint-disable @next/next/no-img-element */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import Router from 'next/router';
import { get, sortBy } from 'lodash';
import Icon from 'sharedComponents/Icon/Icon';
import PlusIconSvgPath from 'svgpath/PlushButtonSvgPath';
import { withTranslation } from 'react-i18next';
import Style from './styles/startApp.module.scss';
import Overview from './Overview';
import { connect } from 'react-redux';
import ExistingProject from './ExisitngProject';
import LockIcon from 'sharedComponents/Icon/LockIcon';
// import SubscriptionJourney from 'sharedComponents/SubscriptionJourney/subscriptionJourney';
import {
  getSubscription,
  setSubscriptionJourneyModal,
} from 'reducer/subscription';
import Button from 'sharedComponents/Button/button';

class Dashboard extends PureComponent {
  constructor() {
    super();
  }

  async componentDidMount() {
    const { getSubscription } = this.props;
    const currentPath = Router.pathname;
    getSubscription(currentPath).then((subscription) => {
      if (!get(subscription, 'userMeta.type', null)) {
        if (currentPath === '/dashboard') {
          Router.push('/profile/addImage');
        }
      }
    });
  }

  renderProjects = () => {
    const {
      projectsList,
      deleteProject,
      collaboratorList,
      createCollaborator,
      fetchCollaboratorList,
      updateOtherDocuments,
      sendReminderToCollaborator,
      fetchCollaborator,
      deleteCollaborator,
      userData,
      removeSectionItems,
      softDeleteProject,
      planFeatures,
      accessibleFeature,
      setSubscriptionJourneyModal,
      subscriptionStatus,
    } = this.props;

    const userId = get(userData, '_id');
    const userEmail = get(userData, 'email');
    const subscriptionType = get(userData, 'userMeta.type');

    const list =
      subscriptionType === 'free'
        ? sortBy(projectsList, [(item) => item.createdAt])
        : projectsList || [];

    return list.map((item, index) => {
      return (
        <div
          className={`${Style.existingAppContainer} pt-5 p-md-5`}
          key={item._id}
        >
          <div>
            <div className={`${Style.existingAppSubContainer}`}>
              <ExistingProject
                handleContinueBtn={this.handleContinueBtn}
                handleSubscriptionModal={this.handleSubscriptionModal}
                projectsList={projectsList}
                planFeatures={planFeatures}
                subscriptionStatus={subscriptionStatus}
                item={item}
                index={index}
                deleteProject={deleteProject}
                collaboratorList={collaboratorList}
                createCollaborator={createCollaborator}
                fetchCollaboratorList={fetchCollaboratorList}
                updateOtherDocuments={updateOtherDocuments}
                sendReminderToCollaborator={sendReminderToCollaborator}
                fetchCollaborator={fetchCollaborator}
                deleteCollaborator={deleteCollaborator}
                userId={userId}
                removeSectionItems={removeSectionItems}
                softDeleteProject={softDeleteProject}
                setSubscriptionJourneyModal={setSubscriptionJourneyModal}
                accessibleFeature={accessibleFeature}
              />

              <Overview
                subscriptionStatus={subscriptionStatus}
                projectsList={projectsList}
                cover={item.cover}
                item={item}
                index={index}
                setSubscriptionJourneyModal={setSubscriptionJourneyModal}
                accessibleFeature={accessibleFeature}
              />
            </div>
          </div>
          {item.projectCollaborator && item.projectCollaborator.length > 0 && (
            <div className="d-flex ml-5">
              {item.projectCollaborator.map((item, index) => {
                return item.email !== userEmail ? (
                  <div
                    key={index}
                    className={`mt-1 ${Style.collaboratorProjectOwnerContainer} d-flex align-items-center`}
                    style={{
                      marginBottom: '10px',
                    }}
                  >
                    <div
                      className={`${Style.creatorDp} d-flex align-items-center col m-0 p-0 rounded-circle`}
                    >
                      <img
                        src={
                          get(item, 'profileImage') ||
                          '/assets/jpg/Placeholder_Avatar_320.jpg'
                        }
                        height="40px"
                        width="40px"
                        className="rounded-circle"
                        alt=""
                        style={{ cursor: 'pointer' }}
                      />
                      <div className={`${Style.creatorNameText} p-2`}>
                        <p className="p3 text-primary">{get(item, 'email')}</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <span className="badge badge-info rounded-pill">
                    Collaborating
                  </span>
                );
              })}
            </div>
          )}
        </div>
      );
    });
  };

  createProject = () => {
    if (
      (this.props.subscriptionStatus !== 'expired' &&
        this.props.accessibleFeature.unlimitedProjects) ||
      this.props.projectsList.length < 2
    ) {
      this.props.setSubscriptionJourneyModal({
        modalStatus: false,
        feature: '',
        cameFrom: 'dashboard',
        onComplete: () => { },
      });
      Router.push('/project/create');
    } else {
      this.props.setSubscriptionJourneyModal({
        modalStatus: true,
        cameFrom: 'dashboard',
        onComplete: this.createProject,
        feature: 'unlimitedProjects',
      });
    }
  };

  render() {
    const { t, accessibleFeature, projectsList, subscriptionStatus } =
      this.props;

    return (
      <div className="container-fluid">
        <div className="my-32 my-md-48 my-lg-48">
          <div className="text-center">
            <div className="row align-items-center">
              <div className="col-md-4 d-none d-md-block"></div>
              <div className="col-12 col-md-4 text-center">
                <h1 className="text-primary mb-3 mb-md-0 fs-20 fs-md-24 fs-lg-24">
                  MY PROJECTS
                </h1>
              </div>
            </div>
            <div className="d-none d-sm-none d-md-block">
              <p className="text-primary text-center p1 mt-3 mb-0 mb-md-34 mb-lg-34">
                Manage and create projects.
              </p>
            </div>
            <div className="d-block d-sm-block d-md-none">
              <p className="text-primary text-center p1 mb-10">
                {t('common:dashboard.mobileInfoMsg')}
              </p>
            </div>
          </div>
          <div className={Style.gridContainer}>
            <div className={Style.newAppContainer}>
              {/* Lock Icon */}
              <div className={Style.lockIconContainer}>
                <LockIcon
                  onClick={() => this.createProject()}
                  height={24}
                  width={24}
                  show={
                    (!accessibleFeature.unlimitedProjects &&
                      projectsList.length >= 2) ||
                    (subscriptionStatus === 'expired' &&
                      projectsList.length >= 2)
                  }
                  viewbox="0 0 24 24"
                />
              </div>
              <div className={`${Style.newAppSubContainer}`}>
                <div className={`${Style.plusContainer}`}>
                  <div
                    data-cy="newProjectContainer"
                    className={`${Style.plusBorder}`}
                    onClick={() => this.createProject()}
                  >
                    <Icon
                      icon={PlusIconSvgPath}
                      color="#00000"
                      iconSize="50px"
                    />
                  </div>
                </div>
                <div className={`${Style.existingTitle}`}>
                  <p
                    className="p1 text-primary text-center mt-32"
                    style={{ cursor: 'pointer' }}
                    onClick={() => this.createProject()}
                  >
                    Create New Project
                  </p>
                </div>
              </div>
            </div>
            {this.renderProjects()}
          </div>
          <div className="row justify-content-center d-flex px-3 px-md-0 px-lg-0">
            {/* <Button
              className="secondary-delete-button text-nowrap fs-14 d-none d-md-block d-lg-block"
              customClass="primary-delete-btn"
              clickHandler={() => Router.push('/deletedProjects')}
              buttonValue={'VIEW RECENTLY DELETED'}
              btntype="button"
              data-cy={`${'View Recently Deleted'}`}
              size="small"
            /> */}
            <Button
              className="secondory-button col-12 col-md-3 text-nowrap fs-16 "
              customClass="cancelButton"
              clickHandler={() => Router.push('/deletedProjects')}
              buttonValue={'VIEW RECENTLY DELETED'}
              btntype="button"
              data-cy={`${'View Recently Deleted'}`}
            />
          </div>
        </div>
      </div>
    );
  }
}
Dashboard.defaultProps = {};

Dashboard.propTypes = {
  t: PropTypes.func.isRequired,
  token: PropTypes.string.isRequired,
  projectsList: PropTypes.array.isRequired,
  getProjectList: PropTypes.func.isRequired,
  deleteProject: PropTypes.func.isRequired,
  deleteCollaborator: PropTypes.func.isRequired,
  fetchCollaborator: PropTypes.func.isRequired,
  sendReminderToCollaborator: PropTypes.func.isRequired,
  updateOtherDocuments: PropTypes.func.isRequired,
  fetchCollaboratorList: PropTypes.func.isRequired,
  createCollaborator: PropTypes.func.isRequired,
  collaboratorList: PropTypes.array.isRequired,
  userData: PropTypes.object.isRequired,
  removeSectionItems: PropTypes.func.isRequired,
};

const mapStateToProps = (state) => ({
  planFeatures: state.subscription.planFeatures,
  currentPlan: state.subscription.currentPlan,
  accessibleFeature: state.subscription.accessibleFeature,
  subscriptionStatus: state.subscription.subscriptionStatus,
  isShowSubscriptionJourny: state.subscription.isShowSubscriptionJourny,
  token: state.auth.token,
});

const mapDistpatchToProps = (dispatch) => {
  return {
    setSubscriptionJourneyModal: (payload) =>
      dispatch(setSubscriptionJourneyModal(payload)),
    getSubscription: (payload) => dispatch(getSubscription(payload)),
  };
};

export default connect(
  mapStateToProps,
  mapDistpatchToProps,
)(withTranslation('common')(Dashboard));
