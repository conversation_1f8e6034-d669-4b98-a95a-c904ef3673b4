import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import options from 'configuration/options.json';
import {
  getLogText,
  renderCommonItem,
  parseSnapshotData,
  sortList,
  applyFilters,
} from 'utils/helper';
import SharedDropdown from 'sharedComponents/CustomDropdown/custom-dropdown';
import Tags from 'sharedComponents/Tags/tags';
import Modal from 'sharedComponents/Modal/modal';
import ToggleSwitch from 'sharedComponents/Toggleswitch/toggle-switch';
import Button from 'sharedComponents/Button/button';
import InlineSvg from 'sharedComponents/inline-svg';
import CustomCarousel from 'sharedComponents/CustomCaraousel/carousel';
import Style from './snapshotCard.module.scss';
import SectionHeader from 'sharedComponents/SectionHeader/sectionHeader';

const SnapshotList = ({
  sectionHeading,
  noProjectsMsg,
  setSnapshotList,
  snapshotList,
  isDashboardView,
  goToSnap,
}) => {
  const [showModal, setShowModal] = useState(false);
  const [filters, setFilters] = useState({
    format: '',
    genre: '',
    setting: '',
    status: '',
    isLoglineOn: false,
    isScriptOn: false,
  });
  const [selectedSort, setSelectedSort] = useState('sortOrder');
  const [isAscending, setIsAscending] = useState(true);
  const [filteredList, setFilteredList] = useState([]);
  const [sortedList, setSortedList] = useState([]);

  const handleSorting = (value) => {
    setSelectedSort(value);
    const sorted = sortList(filteredList, value, isAscending, 'sortOrder');
    setSortedList(sorted);
  };

  const handleSortingOrder = () => {
    setIsAscending((prevState) => {
      const newOrder = !prevState;
      const sorted = sortList(
        filteredList,
        selectedSort,
        newOrder,
        'sortOrder',
      );
      setSortedList(sorted);
      return newOrder;
    });
  };

  const handleFilterChange = (key, value) => {
    setFilters((prevFilters) => ({
      ...prevFilters,
      [key]: value,
    }));
  };

  const applyFilter = () => {
    setShowModal(false);
    const filtered = applyFilters(snapshotList, filters);
    setFilteredList(filtered);
    const sorted = sortList(filtered, selectedSort, isAscending);
    setSortedList(sorted);
  };

  const clearFilter = () => {
    setFilters({
      format: '',
      genre: '',
      setting: '',
      status: '',
      isLoglineOn: false,
      isScriptOn: false,
    });
    setFilteredList(snapshotList);
    const sorted = sortList(snapshotList, selectedSort, isAscending);
    setSortedList(sorted);
    setShowModal(false);
  };

  const renderSnapshotItem = (item) => {
    if (!item) return null;

    const { coverUrl, title, creatorUsername, projectTags, basicInfo } =
      renderCommonItem(item);

    return (
      <div
        key={item._id}
        onClick={() => goToSnap(item)}
        style={{ cursor: 'pointer' }}
      >
        <div
          className="position-relative"
          style={{
            bordreRadius: '6px',
          }}
        >
          <div className={Style.overlay}>
            <div className={Style.snapCard}>
              <h5 className="fs-16">{title}</h5>
              <p className="p2 ">by {creatorUsername}</p>
            </div>
          </div>
          <img
            className={Style.coverDp}
            src={coverUrl}
            width={100}
            height={100}
            alt="cover"
          />
        </div>
        <div className={Style.boxContainer}>
          <div className="row m-0">
            <p className={`p3 ${Style.logText}`}>{getLogText(basicInfo)}</p>
            <Tags
              tags={projectTags}
              textColor="#ECECE0"
              backgroundColor="#05012D"
              showNoTags
            />
          </div>
        </div>
      </div>
    );
  };

  const dropdowns = [
    {
      label: 'Format',
      valueKey: 'format',
      options: options.Format,
    },
    {
      label: 'Genre',
      valueKey: 'genre',
      options: options.generes,
    },
    {
      label: 'Setting',
      valueKey: 'setting',
      options: options.Setting,
    },
    {
      label: 'Status',
      valueKey: 'status',
      options: options.Status,
    },
  ];

  const sortIcon = isAscending
    ? '/assets/svg/sort-vertical.svg'
    : '/assets/svg/sort-vertical-left.svg';

  const isFilterApplied =
    filters.format ||
    filters.genre ||
    filters.setting ||
    filters.status ||
    filters.isLoglineOn ||
    filters.isScriptOn;

  const modalBody = (
    <>
      <div className="row mt-4 m-0">
        {dropdowns.map((dropdown, index) => (
          <div key={index} className="col-12 mb-4 p-0">
            <h5 className="text-left mb-12">{dropdown.label}</h5>
            <SharedDropdown
              options={dropdown.options.map((option) => ({
                key: option,
                value: option,
              }))}
              placeholder="Select"
              value={filters[dropdown.valueKey] || ''}
              onChange={(value) => handleFilterChange(dropdown.valueKey, value)}
              className="custom-dropdown"
            />
          </div>
        ))}
      </div>
      <div className="row">
        <div className="col-12 mb-12">
          <ToggleSwitch
            label="Logline"
            isChecked={filters.isLoglineOn}
            onChange={(isChecked) =>
              handleFilterChange('isLoglineOn', isChecked)
            }
            subHeading="Must Include"
          />
        </div>
        <div className="col-12 my-12">
          <ToggleSwitch
            label="Script"
            isChecked={filters.isScriptOn}
            onChange={(isChecked) =>
              handleFilterChange('isScriptOn', isChecked)
            }
            subHeading="Must Include"
          />
        </div>
      </div>
      <div className="row justify-content-between m-0">
        <div className="col-6 text-left mt-24 mb-0 p-0">
          {isFilterApplied && (
            <Button
              btntype="button"
              customClass="secondary-white-btn"
              clickHandler={clearFilter}
              buttonValue="Clear"
            />
          )}
        </div>
        <div className="col-6 text-right mt-24 mb-0 p-0">
          <Button
            btntype="button"
            customClass="modalBtn"
            clickHandler={applyFilter}
            buttonValue="Apply"
          />
        </div>
      </div>
    </>
  );

  const handleViewMore = () => {
    setSnapshotList(sectionHeading);
  };

  useEffect(() => {
    if (snapshotList && snapshotList.length > 0) {
      // Sort snapshots by sortOrder on initial load
      const sortedSnapshots = snapshotList
        ?.slice() // Create a shallow copy to avoid mutating the original array
        .sort((a, b) => (a.sortOrder || Infinity) - (b.sortOrder || Infinity));

      // Apply additional sorting based on selectedSort and isAscending
      const finalSortedList = sortList(
        sortedSnapshots,
        selectedSort,
        isAscending,
      );

      setFilteredList(finalSortedList); // Update filteredList
      setSortedList(finalSortedList); // Update sortedList
    } else {
      setFilteredList([]);
      setSortedList([]);
    }
  }, [snapshotList, selectedSort, isAscending]);

  return (
    <div className={`${isDashboardView ? 'py-3 py-md-5 py-lg-5' : ''}`}>
      <Modal
        modalShow={showModal}
        title="Filter"
        body={modalBody}
        closeCallback={() => setShowModal(false)}
        modalSize="md"
      />
      <SectionHeader
        title={sectionHeading}
        buttonText="View More"
        onButtonClick={handleViewMore}
        customClass="viewListBtn"
        alignment={isDashboardView ? 'left' : 'center'}
        showButton={isDashboardView}
        showBackButton={!isDashboardView}
        handleBackBtn={() => {
          setSnapshotList(null);
        }}
      />

      {!isDashboardView && (
        <div className="row justify-content-between align-items-center mt-3 m-0">
          <div className="col-3 p-0">
            <Button
              btntype="submit"
              customClass="secondary-white-btn"
              className="py-1 px-8 py-md-1 px-md-3 fw-400"
              clickHandler={() => setShowModal(true)}
              buttonValue="Filter"
            />
          </div>
          <div className="col-9 d-flex justify-content-end align-items-center p-0">
            <h5 className="mr-12 mb-0">Sort By:</h5>
            <SharedDropdown
              options={[
                { key: 'Sort order', value: 'sortOrder' },
                { key: 'Date created', value: 'dateCreated' },
                { key: 'Name', value: 'name' },
              ]}
              value={selectedSort}
              onChange={handleSorting}
              className="custom-dropdown small-dropdown"
              style={{ fontSize: '14px' }}
            />
            <div className="ml-12">
              <InlineSvg
                src={sortIcon}
                onClick={handleSortingOrder}
                alt="sort"
                className={!isAscending ? Style.rotated : ''}
              />
            </div>
          </div>
        </div>
      )}

      {sortedList?.length > 0 ? (
        isDashboardView ? (
          <div className="mt-3">
            <CustomCarousel
              items={sortedList.map(parseSnapshotData).slice(0, 9)}
              renderItem={renderSnapshotItem}
              slidesPerPage={3} // Adjusted for dynamic width
              autoplay={false}
              addBorderBottom={true}
            />
          </div>
        ) : (
          <div className={`mt-5 ${Style.gridContainer}`}>
            {sortedList.map((item) => renderSnapshotItem(item))}
          </div>
        )
      ) : (
        !isDashboardView && <p className="text-center mt-5">{noProjectsMsg}</p>
      )}

      {isDashboardView && (
        <div className="d-flex mt-5 d-md-none d-lg-none justify-content-center">
          <Button
            btntype="button"
            customClass="viewListBtn"
            className="w-100 py-2 px-3"
            buttonValue="View More"
            clickHandler={handleViewMore}
          />
        </div>
      )}
    </div>
  );
};

SnapshotList.propTypes = {
  sectionHeading: PropTypes.string.isRequired,
  noProjectsMsg: PropTypes.string.isRequired,
  setSnapshotList: PropTypes.func.isRequired,
  snapshotList: PropTypes.array.isRequired,
  isDashboardView: PropTypes.bool.isRequired,
  accessibleFeature: PropTypes.object.isRequired,
  subscriptionStatus: PropTypes.string.isRequired,
  setSubscriptionJourneyModal: PropTypes.func.isRequired,
};

export default SnapshotList;
