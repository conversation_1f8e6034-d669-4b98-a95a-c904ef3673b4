import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { withTranslation } from 'react-i18next';
import { get } from 'lodash';
import ProjectCard from 'sharedComponents/ProjectCard/projectCard';
import SectionHeader from 'sharedComponents/SectionHeader/sectionHeader';
import style from '../styles/dashboard.module.scss';
import Button from 'sharedComponents/Button/button';
import CustomCarousel from 'sharedComponents/CustomCaraousel/carousel';

class Dashboard extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      showAll: props.showAllByDefault || false,
      renderSection: '', // Track which section to render locally
    };
  }

  componentDidUpdate(prevProps) {
    // Update showAll when showAllByDefault prop changes
    if (prevProps.showAllByDefault !== this.props.showAllByDefault) {
      this.setState({ showAll: this.props.showAllByDefault });
    }
  }

  handleToggle = () => {
    const { setProjectsList } = this.props;

    // If setProjectsList is provided and defined, call it
    if (typeof setProjectsList === 'function') {
      setProjectsList();
    } else {
      // Otherwise, toggle locally (fallback behavior)
      this.setState((prevState) => ({
        showAll: !prevState.showAll,
      }));
    }
  };

  // Self-contained back navigation that doesn't rely on parent callbacks
  handleNavigateBack = () => {
    console.log('Dashboard: Self-contained navigate back called');

    // Always reset local state first
    this.setState({
      showAll: false,
      renderSection: '',
    });

    // Then try to call parent callback as well if it exists
    const { onNavigateBack } = this.props;
    if (typeof onNavigateBack === 'function') {
      console.log('Also calling parent onNavigateBack');
      onNavigateBack();
    }
  };

  goToSnap = (hash) => {
    if (hash) {
      window.open(`/project/snap/${hash}`, '_blank');
    }
  };

  renderProjectCard = (item) => {
    const body = get(item, 'body');
    const parsedBody = body ? JSON.parse(body) : {};
    const theme = get(parsedBody, 'theme');
    const title = get(parsedBody, 'cover.title', 'Untitled Project');
    const basicInfo = get(parsedBody, 'basicInfo', {});
    const projectTags = get(basicInfo, 'tags', []);
    const validProjectTags = Array.isArray(projectTags) ? projectTags : [];

    return (
      <ProjectCard
        key={item._id}
        item={item}
        theme={theme}
        title={title}
        basicInfo={basicInfo}
        projectTags={validProjectTags}
        onClick={() => {
          this.goToSnap(item.hash);
        }}
      />
    );
  };

  renderProjects = (projectsList, forCarousel = false) => {
    const { showAll } = this.state;
    // Remove the filter that only shows published projects
    const allProjects = projectsList;
    const visibleProjects = showAll ? allProjects : allProjects.slice(0, 3);

    if (forCarousel) {
      // Return array of project cards for the carousel
      return visibleProjects.map(this.renderProjectCard);
    }

    // Return array of project cards for the grid layout
    return visibleProjects.map((item) => this.renderProjectCard(item));
  };

  render() {
    const { sectionHeading, projectsList, noProjectsMsg } = this.props;
    const { showAll } = this.state;

    const hasMoreThanThree =
      projectsList.filter((item) => item.publish).length > 3;

    return (
      <div className="col-12">
        <SectionHeader
          title={sectionHeading}
          showButton={!showAll && hasMoreThanThree}
          buttonText="View More"
          customClass="viewListBtn"
          onButtonClick={this.handleToggle}
          alignment={showAll ? 'center' : 'left'}
          showBackButton={showAll}
          handleBackBtn={() => {
            this.handleNavigateBack();
          }}
          headerTitleClass="fs-20"
        />

        {projectsList.length > 0 ? (
          <div className="py-3 pt-md-4 pb-5 pt-md-4  border-bottom">
            {showAll ? (
              // Show grid layout in "View More" mode
              <div className={`${style.discoverGridContainer}`}>
                {this.renderProjects(projectsList)}
              </div>
            ) : (
              // Show carousel in default mode
              <CustomCarousel
                items={projectsList.slice(0, 9)} /* Show only 15 items */
                renderItem={this.renderProjectCard}
                slidesPerPage={3}
                autoplay={false}
              />
            )}
          </div>
        ) : (
          <p className="text-center text-primary mt-4">
            {noProjectsMsg || 'No projects to be displayed yet.'}
          </p>
        )}
        {/* Mobile View More Button */}
        {!showAll && hasMoreThanThree && (
          <div className="d-flex mt-0 mt-md-5 mt-lg-5 d-md-none d-lg-none justify-content-center pb-32 border-bottom  ">
            <Button
              btntype="button"
              customClass="viewListBtn"
              className="w-100 py-2 px-3"
              buttonValue="View More"
              clickHandler={this.handleToggle}
            />
          </div>
        )}
      </div>
    );
  }
}

Dashboard.defaultProps = {
  showAllByDefault: false,
  onNavigateBack: null,
};

Dashboard.propTypes = {
  projectsList: PropTypes.array.isRequired,
  sectionHeading: PropTypes.string.isRequired,
  noProjectsMsg: PropTypes.string.isRequired,
  setProjectsList: PropTypes.func.isRequired,
  showAllByDefault: PropTypes.bool,
  onNavigateBack: PropTypes.func,
};

export default withTranslation('common')(Dashboard);
