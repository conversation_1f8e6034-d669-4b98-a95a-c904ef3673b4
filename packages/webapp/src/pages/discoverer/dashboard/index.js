import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import Head from 'next/head';
import { get } from 'lodash';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Loader from 'sharedComponents/loader';
import { getUserData } from 'reducer/user';
import { setCachedRoute } from 'reducer/auth';
import { deleteProject, fetchDmProjects, toggleTheme } from 'reducer/project';
import Router from 'next/router';
import Dashboard from './components/dashboard';
import Button from 'sharedComponents/Button/button';
import NewSidebar from 'sharedComponents/NewSidebar/newsidebar';
import { withSubscriptionBanner } from 'sharedComponents/Subscription';
import PageHeaderSection from 'sharedComponents/PageHeaderSection/pageHeaderSection';

class Index extends PureComponent {
  constructor() {
    super();
    this.state = {
      projectsListStatus: false,
      renderSection: null,
      isImageLoading: false,
      showPageHeader: true,
    };
  }

  componentDidMount() {
    const {
      getUserData,
      fetchDmProjects,
      userData,
      toggleTheme,
      token,
      setCachedRoute,
    } = this.props;

    if (!token) {
      setCachedRoute(Router.asPath);
    } else {
      setCachedRoute('');
    }

    toggleTheme('onBoarding');
    const userDataId = get(userData, '_id');
    const query = {
      decisionMakerInfo: { decisionMakerId: userDataId },
    };
    fetchDmProjects(query);
    getUserData();

    // Add a script to ensure the heading is visible on mobile
    if (typeof window !== 'undefined') {
      const style = document.createElement('style');
      style.id = 'discoverer-heading-fix';
      style.innerHTML = `
        @media (max-width: 767px) {
          /* Force display of all section headers */
          .mobile-section-container,
          .section-header-container,
          #mobile-section-header,
          h1, h2, h3, h4, h5, h6 {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: relative !important;
            z-index: 1000 !important;
          }
        }
      `;
      document.head.appendChild(style);
    }
  }

  componentDidUpdate(prevProps) {
    const { accessibleFeature, subscriptionStatus } = this.props;
    if (
      (prevProps.accessibleFeature !== accessibleFeature ||
        prevProps.subscriptionStatus !== subscriptionStatus) &&
      accessibleFeature
    ) {
      if (!accessibleFeature.watchList || subscriptionStatus === 'expired') {
        Router.push('/');
      }
    }
  }

  componentWillUnmount() {
    // Clean up the style element
    if (
      typeof window !== 'undefined' &&
      document.getElementById('discoverer-heading-fix')
    ) {
      document.head.removeChild(
        document.getElementById('discoverer-heading-fix'),
      );
    }
  }

  setImageLoading = (status) => {
    this.setState({ isImageLoading: status });
  };

  goToDiscovery = () => {
    Router.push('/marketPlace', `/marketPlace`);
    window.location.href = `/marketPlace`;
  };

  // Toggle to show a single section
  setProjectsList = (section) => {
    this.setState({
      projectsListStatus: true,
      renderSection: section,
      showPageHeader: false, // Hide PageHeaderSection when setting the project list
    });
  };

  // Reset to show all sections
  handleNavigateBack = () => {
    this.setState({
      projectsListStatus: false,
      renderSection: null,
      showPageHeader: true,
    });
  };

  render() {
    const {
      userData,
      isLoading,
      dmProjectsList,
      bannerWrapper: BannerWrapper,
      bannerProps,
    } = this.props;
    const { projectsListStatus, renderSection, showPageHeader } = this.state;

    const awaitingProjects = [];
    const intrestedProjects = [];
    const notIntrestedProjects = [];

    if (dmProjectsList?.length) {
      dmProjectsList.forEach((item) => {
        const email = userData.email;

        const userActivities = item.activities.filter(
          (snapData) => snapData.user.email === email,
        );

        if (userActivities.length) {
          item.activities = userActivities;

          userActivities.forEach((snapData) => {
            switch (snapData.action) {
              case 'letsTalk':
              case 'tracking':
                intrestedProjects.push(item);
                break;
              case 'notInterested':
                notIntrestedProjects.push(item);
                break;
              case 'view':
              case null:
                awaitingProjects.push(item);
                break;
            }
          });
        }
      });
    }

    const renderContent = () => {
      const sections = [
        {
          id: 'awaiting',
          heading: 'Projects awaiting your response',
          projects: awaitingProjects,
          noMsg: 'No projects shared yet.',
        },
        {
          id: 'interested',
          heading: "Projects I'm interested in",
          projects: intrestedProjects,
          noMsg: 'No projects here.',
        },
        {
          id: 'notInterested',
          heading: 'Not taking this forward',
          projects: notIntrestedProjects,
          noMsg: 'Nothing here yet.',
        },
      ];

      // If we're showing all sections (default view)
      if (!projectsListStatus) {
        return (
          <>
            {showPageHeader && (
              <PageHeaderSection
                title="WATCH LIST"
                showButton
                buttonLabel="View Marketplace"
                onButtonClick={this.goToDiscovery}
              />
            )}

            <p className="text-center text-primary mb-0 mt-3 p2">
              New opportunities for your projects
            </p>

            <div className="row d-md-none my-3">
              <div className="col-12 text-center">
                <Button
                  btntype="submit"
                  customClass="waitListBtn"
                  buttonValue="View Marketplace"
                  clickHandler={this.goToDiscovery}
                >
                  View Marketplace
                </Button>
              </div>
            </div>

            {sections.map(({ id, heading, projects, noMsg }) => (
              <div className="row mt-3 mt-md-5 mt-lg-5" key={heading}>
                <Dashboard
                  sectionHeading={heading}
                  projectsList={projects}
                  noProjectsMsg={noMsg}
                  setProjectsList={() => this.setProjectsList(id)}
                  onNavigateBack={this.handleNavigateBack}
                />
              </div>
            ))}
          </>
        );
      }

      // If we're showing a specific section (detailed view)
      if (projectsListStatus && renderSection) {
        // Find the selected section
        const selectedSection = sections.find(
          (section) => section.id === renderSection,
        );

        if (selectedSection) {
          return (
            <div className="row">
              <Dashboard
                sectionHeading={selectedSection.heading}
                projectsList={selectedSection.projects}
                noProjectsMsg={selectedSection.noMsg}
                setProjectsList={() => {}} // Empty function since we're already in expanded view
                showAllByDefault={true}
                onNavigateBack={this.handleNavigateBack}
              />
            </div>
          );
        }
      }

      // Fallback - should never reach here
      return null;
    };

    return (
      <div className="themeContainer">
        <Head>
          <title>Projects | Smash</title>
          <link rel="shortcut icon" href="/favicon.ico" />
        </Head>

        <div className="container-fluid">
          <div className="row">
            <div className="col-12 col-md-2 col-lg-2 p-0">
              <NewSidebar profile={userData.profile} />
            </div>

            <div className="col-12 col-md-10 col-lg-10 page-wrapper">
              {isLoading ? (
                <Loader />
              ) : (
                <>
                  {BannerWrapper ? (
                    <BannerWrapper bannerProps={bannerProps}>
                      {renderContent()}
                    </BannerWrapper>
                  ) : (
                    renderContent()
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale)),
    },
  };
}

Index.defaultProps = {};

Index.propTypes = {
  userData: PropTypes.object.isRequired,
  getUserData: PropTypes.func.isRequired,
  isLoading: PropTypes.bool.isRequired,
  fetchDmProjects: PropTypes.func.isRequired,
  dmProjectsList: PropTypes.array.isRequired,
  toggleTheme: PropTypes.func.isRequired,
  // Props added by withSubscriptionBanner HOC
  bannerWrapper: PropTypes.elementType,
  bannerProps: PropTypes.object,
};

const mapStateToProps = (state) => ({
  userData: state.auth.userData,
  projectsList: state.project.projectsList,
  projectCount: state.project.projectCount,
  isLoading: state.project.isLoading,
  dmProjectsList: state.project.dmProjectsList,
  token: state.auth.token,
  accessibleFeature: state.subscription.accessibleFeature,
  subscriptionStatus: state.subscription.subscriptionStatus,
});

const mapDispatchToProps = (dispatch) => ({
  getUserData: (payload) => dispatch(getUserData(payload)),
  deleteProject: (payload) => dispatch(deleteProject(payload)),
  fetchDmProjects: (payload) => dispatch(fetchDmProjects(payload)),
  toggleTheme: (payload) => dispatch(toggleTheme(payload)),
  setCachedRoute: (route) => dispatch(setCachedRoute(route)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withSubscriptionBanner(Index));
