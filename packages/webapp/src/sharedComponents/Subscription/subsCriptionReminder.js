import React from 'react';
import InlineSvg from 'sharedComponents/inline-svg';
import styles from './subscriptionReminder.module.scss';
import moment from 'moment';
import { get } from 'lodash';

const subscriptionTexts = {
  trialEnding:
    'Your Smash Pro trial is ending soon! Upgrade now to keep all the Pro features.',
  legacyUpgrade:
    'Upgrade to Smash Pro to access AI tools and all new features.',
  proUpgrade:
    'Create unlimited projects, discover projects and utilise AI tools with Smash Pro.',
  expired: 'Your smash subscription has expired, to renew',
  legacyCampaignText:
    'Legacy Offer: Unlock a year of AI tools and new features for the price of a month.',
  willExpire: (days, plan) =>
    `Your Smash ${plan !== '' ? plan.charAt(0).toUpperCase() + plan.slice(1) : ''} subscription will expire in ${days < 0 ? 0 : days} days, to renew`,
};

function SubsCriptionReminder({
  currentPlan,
  getReminder,
  handleSubscriptionModal,
  isTrial,
  subscriptionCancelled,
  subscription,
  setsubscriptionCancelledStatus,
  subscriptionStatus,
  isLegacy,
}) {
  // No special handling needed

  // Calculate days remaining with proper validation
  const daysRemaining = (() => {
    const expiredTimestamp = get(subscription, 'expired');
    if (!expiredTimestamp || isNaN(expiredTimestamp)) return 0;
    return moment(expiredTimestamp * 1000).diff(moment(), 'days');
  })();
  const getText = () => {
    console.log("subscriptionStatus=========", isLegacy)
    if (subscriptionStatus === 'expired') {
      return (
        subscriptionTexts.expired ||
        'Your subscription has expired. Please renew to continue.'
      );
    } else if (subscriptionCancelled && currentPlan !== 'free' && !isTrial) {
      return subscriptionTexts.willExpire(
        daysRemaining >= 0 ? daysRemaining : 0,
        currentPlan,
      );
    } else if (isTrial) {
      return subscriptionTexts.trialEnding;
    } else if (isLegacy && process.env.legacyCampaignUrl) {
      return subscriptionTexts.legacyCampaignText;
    } else if (isLegacy) {
      return subscriptionTexts.legacyUpgrade;
    } else {
      return subscriptionTexts.proUpgrade;
    }
  };
  // Prepare the banner text based on subscription status
  const daysUntilExpiration = moment(
    get(subscription, 'expired', 0) * 1000,
  ).diff(moment(), 'days');
  const shouldShowBanner = currentPlan && daysUntilExpiration < 30;

  console.log('SubsCriptionReminder conditions:', {
    currentPlan,
    daysUntilExpiration,
    shouldShowBanner,
    subscription,
    subscriptionStatus,
    isTrial,
  });

  return (
    shouldShowBanner && (
      <div
        className={`${(subscriptionCancelled && currentPlan !== 'free' && !isTrial) ||
          subscriptionStatus === 'expired'
          ? styles.remainingDays
          : styles.subscriptionRequest
          } w-100`}
      >
        <div className={`${styles.subscriptionInfo}`}>
          <span className={styles.subscriptionText}>{getText()}</span>
          <span
            className={`${styles.upgradeText} cursor-pointer`}
            onClick={handleSubscriptionModal}
          >
            {subscriptionStatus === 'expired' ||
              (subscriptionCancelled && currentPlan !== 'free' && !isTrial)
              ? 'click here'
              : 'Upgrade now'}
          </span>
          <span className={styles.subscriptionText}>.</span>
        </div>
      </div>
    )
  );
}

export default SubsCriptionReminder;
