import React from 'react';
import PropTypes from 'prop-types';
import SubsCriptionReminder from './subsCriptionReminder';
import styles from './SubscriptionBanner.module.scss';

/**
 * SubscriptionBanner - A component to display subscription reminders and notifications
 * This component is designed to be used at the layout level, not within the sidebar
 *
 * @param {Object} props - Component props
 * @param {string} props.subscriptionStatus - Current subscription status
 * @param {string} props.currentPlan - Current subscription plan
 * @param {boolean} props.isTrial - Whether user is in trial period
 * @param {function} props.getReminder - Function to get reminder status
 * @param {function} props.handleSubscriptionModal - Function to handle subscription modal
 * @param {Object} props.subscription - Subscription details
 * @param {boolean} props.isLegacy - Whether subscription is legacy
 * @param {boolean} props.subscriptionCancelled - Whether subscription is cancelled
 * @param {function} props.setsubscriptionCancelledStatus - Function to set subscription cancelled status
 * @param {boolean} props.isMobile - Whether the view is mobile
 * @returns {JSX.Element|null} - Rendered component or null if banner should not be shown
 */
const SubscriptionBanner = ({
  subscriptionStatus,
  currentPlan,
  isTrial,
  getReminder,
  handleSubscriptionModal,
  subscription,
  isLegacy,
  subscriptionReminder,
  subscriptionCancelled,
  setsubscriptionCancelledStatus,
}) => {
  // Check if banner should be displayed
  const shouldShowBanner =
    subscriptionReminder ||
    subscriptionCancelled ||
    subscriptionStatus === 'expired' ||
    isTrial;

  console.log('SubscriptionBanner conditions:', {
    subscriptionReminder,
    subscriptionCancelled,
    subscriptionStatus,
    isTrial,
    shouldShowBanner,
    currentPlan,
  });

  if (!shouldShowBanner) {
    return null;
  }

  return (
    <div className={styles.bannerContainer}>
      <SubsCriptionReminder
        subscriptionStatus={subscriptionStatus}
        currentPlan={currentPlan}
        isTrial={isTrial}
        getReminder={getReminder}
        handleSubscriptionModal={handleSubscriptionModal}
        subscription={subscription}
        isLegacy={isLegacy}
        subscriptionCancelled={subscriptionCancelled}
        setsubscriptionCancelledStatus={setsubscriptionCancelledStatus}
      />
    </div>
  );
};

SubscriptionBanner.propTypes = {
  subscriptionStatus: PropTypes.string,
  currentPlan: PropTypes.string,
  isTrial: PropTypes.bool,
  getReminder: PropTypes.func.isRequired,
  handleSubscriptionModal: PropTypes.func.isRequired,
  subscription: PropTypes.object,
  isLegacy: PropTypes.bool,
  subscriptionReminder: PropTypes.bool,
  subscriptionCancelled: PropTypes.bool,
  setsubscriptionCancelledStatus: PropTypes.func.isRequired,
  isMobile: PropTypes.bool,
};

SubscriptionBanner.defaultProps = {
  subscriptionStatus: '',
  currentPlan: '',
  isTrial: false,
  subscription: {},
  isLegacy: false,
  subscriptionReminder: false,
  subscriptionCancelled: false,
  isMobile: false,
};

export default SubscriptionBanner;
