.bannerWrapper {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.fullWidthBanner {
  margin-left: -20px;
  margin-right: -20px;
  width: calc(100% + 40px);
}

/* Content styles */
.contentWithBanner {
  position: relative;
  z-index: 1;
}

.contentWithoutBanner {
  position: relative;
  z-index: 1;
}

/* For mobile view */
@media (max-width: 768px) {
  .bannerWrapper {
    position: relative;
    margin: 0;
    padding: 0;
  }

  .fullWidthBanner {
    margin-left: -16px;
    margin-right: -16px;
    width: calc(100% + 32px);
    margin-bottom: 0;
  }

  /* We don't need mobile-specific margin anymore */
}
