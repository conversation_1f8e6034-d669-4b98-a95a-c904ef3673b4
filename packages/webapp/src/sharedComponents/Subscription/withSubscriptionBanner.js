import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { get } from 'lodash';
import { getReminder, setSubscriptionJourneyModal } from 'reducer/subscription';
import PageContentWrapper from './PageContentWrapper';

/**
 * Higher-order component to add subscription banner to pages that use NewSidebar directly
 *
 * @param {React.ComponentType} Component - The component to wrap
 * @returns {React.ComponentType} - Wrapped component with subscription banner
 */
const withSubscriptionBanner = (Component) => {
  const WrappedComponent = (props) => {
    const dispatch = useDispatch();
    const [subscriptionCancelled, setSubscriptionCancelled] = useState(false);
    const [localSubscriptionReminder, setLocalSubscriptionReminder] = useState(null);

    // Get subscription data from Redux store
    const subscriptionReminder = useSelector(
      (state) => state.subscription.subscriptionReminder,
    );
    const subscriptionStatus = useSelector(
      (state) => state.subscription.subscriptionStatus,
    );
    const currentPlan = useSelector((state) => state.subscription.currentPlan);
    const isTrial = useSelector((state) => state.subscription.isTrial);
    const isMobile = window.innerWidth <= 768;

    // Initialize local reminder state based on user plan and maintain it
    React.useEffect(() => {
      if (localSubscriptionReminder === null && currentPlan) {
        const profile = props.userData?.profile || {};
        const isLegacy = get(profile, 'subscription.isLegacy');

        // Determine if banner should be shown based on plan
        const shouldShowReminder =
          currentPlan === 'free' ||
          currentPlan === 'legacy' ||
          isLegacy ||
          isTrial ||
          subscriptionStatus === 'expired';

        setLocalSubscriptionReminder(shouldShowReminder);
      }
    }, [currentPlan, isTrial, subscriptionStatus, props.userData, localSubscriptionReminder]);

    // Handle subscription modal
    const handleSubscription = () => {
      dispatch(
        setSubscriptionJourneyModal({
          modalStatus: true,
          feature: 'legacy', // Changed from 'learnSmashPro' to 'legacy' to show Smash Pro upgrade
          onComplete: () => { },
        }),
      );
    };

    // Close subscription reminder
    const closeSubscriptionReminder = () => {
      setSubscriptionCancelled(false);
      setLocalSubscriptionReminder(false);
    };

    // Get profile from props
    const profile = props.userData?.profile || {};

    // Use local reminder state if available, otherwise fall back to Redux state
    const effectiveSubscriptionReminder = localSubscriptionReminder !== null
      ? localSubscriptionReminder
      : subscriptionReminder;

    // Create banner props
    const bannerProps = {
      subscriptionStatus,
      currentPlan,
      isTrial,
      getReminder: closeSubscriptionReminder, // Use local close function instead of Redux action
      handleSubscriptionModal: handleSubscription,
      subscription: profile?.subscription,
      isLegacy: get(profile, 'subscription.isLegacy'),
      subscriptionReminder: effectiveSubscriptionReminder,
      subscriptionCancelled,
      setsubscriptionCancelledStatus: closeSubscriptionReminder,
      isMobile,
    };
    // Render the original component with the banner wrapper
    return (
      <Component
        {...props}
        bannerWrapper={PageContentWrapper}
        bannerProps={bannerProps}
      />
    );
  };

  // Set display name for debugging
  WrappedComponent.displayName = `withSubscriptionBanner(${Component.displayName || Component.name || 'Component'})`;

  return WrappedComponent;
};

export default withSubscriptionBanner;
