import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import SubscriptionBanner from './SubscriptionBanner';
import styles from './PageContentWrapper.module.scss';

/**
 * PageContentWrapper - A component to wrap page content and include the subscription banner
 * This component is designed to be used in pages that use NewSidebar directly
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render
 * @param {Object} props.bannerProps - Props to pass to the SubscriptionBanner component
 * @returns {JSX.Element} - Rendered component
 */
const PageContentWrapper = ({ children, bannerProps }) => {
  // Check if banner should be displayed
  const shouldShowBanner =
    bannerProps.subscriptionReminder ||
    bannerProps.subscriptionCancelled ||
    bannerProps.subscriptionStatus === 'expired' ||
    bannerProps.isTrial;

  // Check if we're on mobile
  const [isMobile, setIsMobile] = useState(false);

  // Use useEffect to detect mobile view
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    // Set initial value
    if (typeof window !== 'undefined') {
      handleResize();

      // Add event listener
      window.addEventListener('resize', handleResize);

      // Clean up
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, []);

  return (
    <div className={styles.bannerWrapper}>
      {/* Only show banner if it should be displayed */}
      {shouldShowBanner && (
        <div className={styles.fullWidthBanner}>
          <SubscriptionBanner {...bannerProps} />
        </div>
      )}

      {/* Page content with appropriate class based on banner visibility */}
      <div
        className={
          shouldShowBanner
            ? styles.contentWithBanner
            : styles.contentWithoutBanner
        }
        style={{
          // Different margin logic based on device and banner state
          marginTop: isMobile
            ? shouldShowBanner
              ? '32px'
              : '48px' // Mobile: 32px with banner, 48px without
            : '32px', // Desktop: always 32px
        }}
      >
        {children}
      </div>
    </div>
  );
};

PageContentWrapper.propTypes = {
  children: PropTypes.node.isRequired,
  bannerProps: PropTypes.object.isRequired,
};

export default PageContentWrapper;
