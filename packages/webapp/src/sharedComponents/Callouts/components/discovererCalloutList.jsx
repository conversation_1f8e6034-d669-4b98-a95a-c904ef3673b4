import React from 'react';
import CallOutList from 'sharedComponents/Callouts/components/callOutList';
import SectionHeader from 'sharedComponents/SectionHeader/sectionHeader';

const DiscoverCallOutList = ({
  callOutList,
  userData,
  router,
  actionCtaBtn = true,
  pageType,
  callOutContainer,
  referrer = 'smash',
}) => {
  const { snap } = router.query;

  // ✅ Filter only published callouts NOT created by the current user
  const filteredCallOuts = callOutList.filter((callout) => {
    return callout.discoverer?.id !== userData?._id;
  });

  return (
    <div
      className={`row justify-content-center ${pageType === 'discovererCallouts' && filteredCallOuts.length > 0 ? 'mt-32' : ''}`}
    >
      <div className={`${callOutContainer} mb-3`}>
        {actionCtaBtn && (
          <SectionHeader
            title="Discover call outs"
            alignment="left"
            headerTitleClass="fs-20"
          />
        )}
      </div>
      <CallOutList
        callOutList={filteredCallOuts}
        userData={userData}
        snapId={snap}
        pageType={pageType}
        referrer={referrer}
      />
    </div>
  );
};

export default DiscoverCallOutList;
