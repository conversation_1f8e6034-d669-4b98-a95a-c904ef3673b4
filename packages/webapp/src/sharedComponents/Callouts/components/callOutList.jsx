import React, { useState } from 'react';
import PropTypes from 'prop-types';
import Router from 'next/router';
import { withTranslation } from 'react-i18next';
import { get } from 'lodash';
// import Image from 'next/image';
import Style from '../styles/callout.module.scss';
// import Button from 'sharedComponents/Button/button';
import mixpanelAnalytics from 'lib/mixpanel';
import UserAvatar from 'sharedComponents/UserAvatar/userAvatar';
import SectionHeader from 'sharedComponents/SectionHeader/sectionHeader';

const CallOutList = ({
  callOutList,
  source,
  snapId,
  pageType,
  listContainer,
  referrer = 'smash',
}) => {
  const [isImageloading, setIsImageLoading] = useState(true);

  const handleImageLoading = () => {
    setIsImageLoading(false);
  };

  const goToSnap = (id) => {
    const query = snapId ? { snap: snapId } : {};

    switch (pageType) {
      case 'discovererCallouts':
        Router.push({
          pathname: `/callouts/${id}`,
          query,
        });
        break;
      case 'publicCallouts':
        Router.push({
          pathname: `/callouts/public/${id}/${referrer}`,
          query,
        });
        break;
      default:
        if (source === 'mycallout') {
          Router.push(`/callouts/${id}/slates`);
        }
        break;
    }
    mixpanelAnalytics('submit_to_callout', {
      pageName: 'callout_list',
      calloutName: get(
        callOutList.find((item) => item.id === id),
        'name',
      ),
    });
  };

  return (
    <div className="col-12">
      {pageType !== 'discovererCallouts' && callOutList.length > 0 && (
        <SectionHeader
          title="My Callouts"
          alignment="left"
          headerTitleClass="fs-20"
        />
      )}
      <div
        className={`${Style.gridContainer} ${listContainer} ${pageType !== 'discovererCallouts' && callOutList.length > 0 ? 'pt-3 pb-32' : ''}`}
        style={{
          borderBottom:
            pageType !== 'discovererCallouts' && callOutList.length > 0
              ? '1px solid #dbdbcf'
              : 'none',
        }}
      >
        {callOutList.map((item) => {
          return (
            <div
              onClick={() => goToSnap(item.id)}
              className={`${Style.calloutContainer}`}
              key={item._id}
              style={{
                borderRadius: '6px',
              }}
            >
              <div className={Style.imageWrapper}>
                <img
                  className={`${Style.coverDp} ${isImageloading ? Style.imageLoader : ''}`}
                  src={get(item, 'body.logo')}
                  width={100}
                  height={100}
                  alt="cover"
                  onLoad={handleImageLoading}
                />
              </div>
              <div className={Style.boxContainer}>
                <h4 className="mb-1 fw-bold text-uppercase px-12">
                  {get(item, 'name', '')}
                </h4>
                <div className="d-flex align-items-start mt-3 px-12">
                  {/* Avatar */}
                  <UserAvatar
                    src={
                      pageType === 'discovererCallouts'
                        ? get(item, 'discoverer.avatar')
                        : get(item, 'creator.avatar')
                    }
                    size="md"
                  />

                  {/* Title + Metadata block */}
                  <div style={{ marginLeft: '12px' }}>
                    <p className="mb-1 p2">
                      {get(item, 'body.companyName', '')}
                    </p>
                    <p className="text-muted mb-0 p2">
                      {new Date(get(item, 'createdAt', '')).toLocaleDateString(
                        'en-GB',
                        {
                          day: '2-digit',
                          month: 'long',
                          year: 'numeric',
                        },
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

CallOutList.propTypes = {
  callOutList: PropTypes.array.isRequired,
};

export default withTranslation('common')(CallOutList);
