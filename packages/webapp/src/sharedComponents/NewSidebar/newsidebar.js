import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { get } from 'lodash';
import { connect } from 'react-redux';
import { withRouter } from 'next/router';
import Router, { useRouter } from 'next/router';
import { logout } from 'reducer/auth';
import { getSubscription } from 'reducer/subscription';
import SmashLogo from 'sharedComponents/SmashLogo/smashLogo';
import DashboardHeader from 'sharedComponents/Header/dashboardHeader';
import styles from './sidebar.module.scss';
import SidebarNavLinks from './sidebarLink';
import ProfileSection from './profileSection';

const NewSidebar = ({
  profile,
  noAvatar,
  noLogoLink,
  subscriptionStatus,
  currentPlan,
  logout,
  getSubscription,
  accessibleFeature,
}) => {
  const { pathname } = useRouter();
  const [isMobile, setIsMobile] = useState(false);
  const profileImage = get(profile, 'profileImage', '');
  const userName = get(profile, 'name.fullName', '');

  useEffect(() => {
    const handleResize = () => {
      console.log(window.innerWidth <= 768);
      setIsMobile(window.innerWidth <= 768);
    };

    // Handle window resize
    handleResize();
    window.addEventListener('resize', handleResize);

    // Fetch subscription data only on initial mount
    getSubscription(pathname).catch((err) => {
      console.log(err);
    });

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [getSubscription, pathname]);

  // Ensure currentPlan is loaded when component mounts
  useEffect(() => {
    if (!currentPlan) {
      // If currentPlan is empty, fetch subscription data
      getSubscription('').catch((err) => {
        console.log(err);
      });
    }
  }, [currentPlan, getSubscription]);

  const handleSmashNavigation = () => {
    if (!noLogoLink) {
      Router.push('/myaccount');
    }
  };

  const handleLogo = () => {
    if (!noLogoLink) {
      Router.push('/mydashboard');
    }
  };

  const logoutRedirect = () => {
    logout();
    Router.push('/login');
  };

  // Removed subscription-related functions as they're now handled by the layout

  const isActive = (path) => pathname === path;

  // No longer need to add styles to the head as the banner is now handled by the layout

  return (
    <>
      {/* Subscription banner is now handled by the layout */}
      <div
        className={`themeContainer ${isMobile ? 'mobile' : 'desktop'}`}
        style={{
          marginTop: 0,
          paddingTop: 0,
        }}
      >
        {isMobile ? (
          // Mobile Header
          <div className={`${styles.fixedHeader}`}>
            <DashboardHeader profile={profile} />
          </div>
        ) : (
          // Desktop Sidebar
          <div
            className={`${styles.sidebar} d-flex flex-column`}
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              marginTop: 0,
              paddingTop: '16px',
            }}
          >
            {/* Logo */}
            <div className={`${styles.logo} py-3 border-bottom`}>
              <SmashLogo
                src={'/assets/svg/smashNewLogo.svg'}
                width="133px"
                height="40px"
                noLogoLink={noLogoLink}
                variant={currentPlan}
                style={{ cursor: 'pointer' }}
                onClick={handleLogo}
              />
            </div>

            {/* Navigation Links */}
            <SidebarNavLinks
              isActive={isActive}
              subscriptionStatus={subscriptionStatus}
              currentPlan={currentPlan}
              accessibleFeature={accessibleFeature}
            />

            {/* Profile Section */}
            <ProfileSection
              profileImage={profileImage}
              userName={userName}
              handleSmashNavigation={handleSmashNavigation}
              logoutRedirect={logoutRedirect}
              noAvatar={noAvatar}
            />
          </div>
        )}
      </div>
    </>
  );
};

NewSidebar.propTypes = {
  profile: PropTypes.object,
  noAvatar: PropTypes.bool,
  noLogoLink: PropTypes.bool,
  subscriptionStatus: PropTypes.string,
  currentPlan: PropTypes.string,
  logout: PropTypes.func.isRequired,
  getSubscription: PropTypes.func.isRequired,
  accessibleFeature: PropTypes.array,
};

NewSidebar.defaultProps = {
  profile: {},
  noAvatar: false,
  noLogoLink: false,
  subscriptionStatus: '',
  currentPlan: '',
  accessibleFeature: [],
};

const mapStateToProps = (state) => ({
  currentPlan: state.subscription.currentPlan,
  accessibleFeature: state.subscription.accessibleFeature,
  subscriptionStatus: state.subscription.subscriptionStatus,
});

const mapDispatchToProps = (dispatch) => {
  return {
    logout: (payload) => dispatch(logout(payload)),
    getSubscription: (pathname) => dispatch(getSubscription(pathname)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withRouter(NewSidebar));
