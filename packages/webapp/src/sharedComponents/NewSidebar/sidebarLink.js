import React from 'react';
import Router from 'next/router';
import styles from './sidebar.module.scss';
import LockIcon from 'sharedComponents/Icon/LockIcon';
import { useDispatch } from 'react-redux';
import { setSubscriptionJourneyModal } from 'reducer/subscription';

const navLinks = [
  { label: 'DASHBOARD', href: '/mydashboard' },
  { label: 'PROJECTS', href: '/dashboard' },
  { label: 'CALL OUTS', href: '/callouts' },
  { label: 'MARKETPLACE', href: '/marketPlace' },
  { label: 'WATCH LIST', href: '/discoverer/dashboard' },
];

const SidebarNavLinks = ({
  isActive,
  subscriptionStatus,
  currentPlan,
  accessibleFeature,
}) => {
  const dispatch = useDispatch();

  const isProOrEnterprise =
    currentPlan === 'pro' || currentPlan === 'enterprise';

  const handleWathList = () => {
    if (accessibleFeature.watchList && subscriptionStatus !== 'expired') {
      dispatch(
        setSubscriptionJourneyModal({
          modalStatus: false,
          feature: '',
          onComplete: () => {},
        }),
        Router.push('/discoverer/dashboard'),
      );
    } else {
      dispatch(
        setSubscriptionJourneyModal({
          modalStatus: true,
          cameFrom: 'dashboard',
          onComplete: handleWathList,
          feature: 'watchList',
        }),
      );
    }
  };

  return (
    <div
      className="flex-grow-1"
      style={{ overflowY: 'auto', paddingBottom: '1rem' }}
    >
      <div className={styles.sidebarNav}>
        <ul>
          {navLinks.map(({ label, href }) => (
            <li key={href}>
              <a
                href={href}
                onClick={(e) => {
                  if (href === '/discoverer/dashboard') {
                    e.preventDefault();
                    handleWathList();
                  }
                }}
                className={`${styles.activeBtn} ${isActive(href) ? styles.active : ''}`}
                style={{ textDecoration: 'none', cursor: 'pointer' }}
              >
                <span
                  style={{
                    display: 'inline-block',
                    borderBottom: isActive(href) ? '1px solid #000' : 'none',
                    width: 'fit-content',
                    paddingBottom: '2px',
                  }}
                >
                  {label}
                  {href === '/discoverer/dashboard' &&
                    (!isProOrEnterprise ||
                      subscriptionStatus === 'expired') && (
                      <span className="ml-2">
                        <LockIcon
                          height={16}
                          width={16}
                          show={
                            !accessibleFeature.watchList ||
                            subscriptionStatus === 'expired'
                          }
                        />
                      </span>
                    )}
                </span>
              </a>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default SidebarNavLinks;
