/* eslint-disable @next/next/no-img-element */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import _, { get } from 'lodash';
import { connect } from 'react-redux';
import Router, { withRouter } from 'next/router';
import BackIconSvgPath from 'svgpath/BackIconSvgPath';
import { logout } from 'reducer/auth';
import DropDown from 'sharedComponents/dropDown/dropdown';
import style from '../styles/dashboardHeader.module.scss';
import mobileBannerStyle from '../styles/mobileBannerAdjustment.module.scss';
import Button from '../Button/button';
import {
  getReminder,
  getSubscription,
  setSubscriptionJourneyModal,
  getPlans,
  getManageSubscriptionLink,
  getStripeCheckoutLink,
} from 'reducer/subscription';
import SubsCriptionReminder from 'sharedComponents/Subscription/subsCriptionReminder';
import SmashLogo from 'sharedComponents/SmashLogo/smashLogo';
import mixpanelAnalytics from 'lib/mixpanel';

/**
 * This reusuable dashboard header component will display a company log on left and logout to the right.
 */
class DashboardHeader extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      showDropdown: '',
      subscription: {},
      subscriptionCancelled: false,
      currentPlanId: '',
      currentPlanType: '',
      userEmail: '',
    };
  }

  componentDidMount() {
    const { getSubscription } = this.props;
    const currentPath = Router.asPath;
    window.scrollTo(0, 0);

    // Add a direct style fix for mobile
    const style = document.createElement('style');
    style.id = 'mobile-banner-fix';

    // Check if the banner should be visible
    const isBannerVisible =
      this.props.subscriptionReminder ||
      this.state.subscriptionCancelled ||
      this.props.subscriptionStatus === 'expired' ||
      this.props.isTrial;

    if (isBannerVisible) {
      // Styles when banner is visible
      style.innerHTML = `
        @media (max-width: 767px) {
          /* Fix for mobile view when banner is visible */
          .col-12.col-md-10.col-lg-10 {
            margin-top: 40px !important;
          }

          /* Target the container */
       

          /* Target the MY PROJECTS heading */
          h1.text-primary.mb-3.mb-md-0 {
            margin-top: 40px !important;
          }

          /* Target the mobile info message */
          p.text-primary.text-center.p1.mb-10 {
            margin-top: 40px !important;
          }
        }
      `;
    } else {
      // Styles when banner is NOT visible - remove margins
      style.innerHTML = `
        @media (max-width: 767px) {
          /* Remove margins when banner is not visible */
          .col-12.col-md-10.col-lg-10 {
            margin-top: 0 !important;
          }

          /* Target the container */
        

          /* Target the MY PROJECTS heading */
          h1.text-primary.mb-3.mb-md-0 {
            margin-top: 0 !important;
          }

          /* Target the mobile info message */
          p.text-primary.text-center.p1.mb-10 {
            margin-top: 0 !important;
          }
        }
      `;
    }

    document.head.appendChild(style);

    getSubscription(currentPath)
      .then((data) => {
        const subscriptionCancelled = get(data, 'userMeta.cancelAtPeriodEnd');

        this.setState({
          subscription: get(data, 'userMeta'),
          loading: false,
          subscriptionCancelled,
          currentPlanId: get(data, 'userMeta.planId'),
          currentPlanType: get(data, 'userMeta.type'),
          userEmail: get(data, 'email'),
        });
      })
      .catch((err) => {
        console.log(err);
        this.setState({
          loading: false,
        });
      });
  }

  componentDidUpdate(prevProps, prevState) {
    // Check if banner visibility has changed
    const wasBannerVisible =
      prevProps.subscriptionReminder ||
      prevState.subscriptionCancelled ||
      prevProps.subscriptionStatus === 'expired' ||
      prevProps.isTrial;

    const isBannerVisible =
      this.props.subscriptionReminder ||
      this.state.subscriptionCancelled ||
      this.props.subscriptionStatus === 'expired' ||
      this.props.isTrial;

    // If banner visibility has changed, update the styles
    if (wasBannerVisible !== isBannerVisible) {
      const styleElement = document.getElementById('mobile-banner-fix');
      if (styleElement) {
        document.head.removeChild(styleElement);
      }

      // Create a new style element
      const style = document.createElement('style');
      style.id = 'mobile-banner-fix';

      if (isBannerVisible) {
        // Styles when banner is visible
        style.innerHTML = `
          @media (max-width: 767px) {
            /* Fix for mobile view when banner is visible */
            .col-12.col-md-10.col-lg-10 {
              margin-top: 0 !important;
            }

            /* Target the container */
            .container {
              margin-top: 0 !important;
            }

            /* Target the MY PROJECTS heading */
            h1.text-primary.mb-3.mb-md-0 {
              margin-top: 0 !important;
            }

            /* Target the mobile info message */
            p.text-primary.text-center.p1.mb-10 {
              margin-top: 0 !important;
            }

            /* Ensure proper spacing for page content */
            #page-content-wrapper {
              margin-top: 48px !important;
            }
          }
        `;
      } else {
        // Styles when banner is NOT visible - remove margins
        style.innerHTML = `
          @media (max-width: 767px) {
            /* Remove margins when banner is not visible */
            .col-12.col-md-10.col-lg-10 {
              margin-top: 0 !important;
            }

            /* Target the container */
            .container {
              margin-top: 0 !important;
            }

            /* Target the MY PROJECTS heading */
            h1.text-primary.mb-3.mb-md-0 {
              margin-top: 0 !important;
            }

            /* Target the mobile info message */
            p.text-primary.text-center.p1.mb-10 {
              margin-top: 0 !important;
            }

            /* Ensure proper spacing for page content */
            #page-content-wrapper {
              margin-top: 48px !important;
            }

            /* Ensure section headers are visible */
            .section-header-container,
            .mobile-section-container,
            .mobile-title-section,
            #mobile-section-title,
            #mobile-section-description,
            #marketplace-header,
            #marketplace-header-no-banner,
            .marketplace-header,
            #mobile-section-header,
            h1, h2, h3, h4, h5, h6,
            .SectionHeader {
              display: block !important;
              visibility: visible !important;
              opacity: 1 !important;
              position: relative !important;
              z-index: 1000 !important;
            }
          }
        `;
      }

      document.head.appendChild(style);
    }
  }

  componentWillUnmount() {
    // Clean up - remove the style element
    const styleElement = document.getElementById('mobile-banner-fix');
    if (styleElement) {
      document.head.removeChild(styleElement);
    }
  }

  handleSubscriptionModal = async () => {
    const {
      setSubscriptionJourneyModal,
      getManageSubscriptionLink,
      currentPlan,
      subscriptionStatus,
      getStripeCheckoutLink,
      isTrial,
    } = this.props;
    const { currentPlanId, currentPlanType, userEmail } = this.state;
    if (
      (this.state.subscriptionCancelled &&
        currentPlan !== 'free' &&
        !isTrial) ||
      subscriptionStatus === 'expired'
    ) {
      if (subscriptionStatus === 'expired') {
        const payload = {
          planId: currentPlanId,
          type: currentPlanType,
          email: userEmail,
        };
        getStripeCheckoutLink(payload).then((checkoutLink) => {
          window.open(checkoutLink);
        });
      } else {
        await getManageSubscriptionLink()
          .then((url) => {
            window.open(url, '_blank');
          })
          .catch((err) => {
            console.log(err);
          });
      }
    }
    // else if (get(subscription, 'isLegacy') && process.env.legacyCampaignUrl) {
    //   Router.push(
    //     `${process.env.LoginUrl}/offer/${process.env.legacyCampaignUrl}`,
    //   );
    // }
    else {
      setSubscriptionJourneyModal({
        modalStatus: true,
        feature: this.props.currentPlan === 'legacy' ? 'legacy' : 'proTrial',
        cameFrom: 'dashboard',
        onComplete: this.closeModal,
      });
    }
  };

  closeModal = () => {
    this.props.setSubscriptionJourneyModal({
      modalStatus: false,
      feature: '',
      onComplete: () => {},
    });
    window.location.reload();
  };

  showDropdown = () => {
    this.setState({
      showDropdown: 'visibledropdown',
    });
  };

  hideDropdown = () => {
    this.setState({ showDropdown: '' });
  };

  logoutRedirect = () => {
    const { logout } = this.props;
    logout(this.props);
  };

  handleWathList = () => {
    const { setSubscriptionJourneyModal, accessibleFeature } = this.props;
    if (
      accessibleFeature.watchList &&
      this.props.subscriptionStatus !== 'expired'
    ) {
      setSubscriptionJourneyModal({
        modalStatus: false,
        feature: '',
        onComplete: () => {},
      }),
        Router.push('/discoverer/dashboard');
    } else {
      setSubscriptionJourneyModal({
        modalStatus: true,
        cameFrom: 'dashboard',
        onComplete: this.handleWathList,
        feature: 'watchList',
      });
    }
  };

  setsubscriptionCancelledStatus = () => {
    // Update state
    this.setState(
      {
        subscriptionCancelled: false,
      },
      () => {
        // After state is updated, check if banner should still be visible
        const isBannerVisible =
          this.props.subscriptionReminder ||
          this.state.subscriptionCancelled ||
          this.props.subscriptionStatus === 'expired' ||
          this.props.isTrial;

        // If banner is no longer visible, update the styles
        if (!isBannerVisible) {
          const styleElement = document.getElementById('mobile-banner-fix');
          if (styleElement) {
            document.head.removeChild(styleElement);
          }

          // Create a new style element to remove margins
          const style = document.createElement('style');
          style.id = 'mobile-banner-fix';
          style.innerHTML = `
          @media (max-width: 767px) {
            /* Remove margins when banner is not visible */
            .col-12.col-md-10.col-lg-10 {
              margin-top: 0 !important;
            }

            /* Target the container */
            .container {
              margin-top: 0 !important;
            }

            /* Target the MY PROJECTS heading */
            h1.text-primary.mb-3.mb-md-0 {
              margin-top: 0 !important;
            }

            /* Target the mobile info message */
            p.text-primary.text-center.p1.mb-10 {
              margin-top: 0 !important;
            }

            /* Ensure proper spacing for page content */
            #page-content-wrapper {
              margin-top: 48px !important;
            }

            /* Ensure section headers are visible */
            .section-header-container,
            .mobile-section-container,
            .mobile-title-section,
            #mobile-section-title,
            #mobile-section-description,
            #marketplace-header,
            #marketplace-header-no-banner,
            .marketplace-header,
            #mobile-section-header,
            h1, h2, h3, h4, h5, h6,
            .SectionHeader {
              display: block !important;
              visibility: visible !important;
              opacity: 1 !important;
              position: relative !important;
              z-index: 1000 !important;
            }
          }
        `;
          document.head.appendChild(style);
        }
      },
    );
  };

  // Method to handle banner visibility - simplified
  updateBannerVisibility = () => {
    // No special handling needed anymore
  };

  handleNavigation = () => {
    const { noLogoLink } = this.props;
    if (!noLogoLink) {
      Router.push('/mydashboard');
    }
  };

  render() {
    const {
      noAvatar,
      profile,
      children,
      fixedtop,
      clickHandler,
      btnName,
      subscriptionReminder,
      accessibleFeature,
      isTrial,
      noLogoLink,
    } = this.props;
    const { showDropdown, subscription, subscriptionCancelled } = this.state;
    const profileImage = _.get(profile, 'profileImage');
    const dropdownOptions = [
      {
        icon: '',
        uploadControl: false,
        handler: () => Router.push('/mydashboard'),
        value: 'DASHBOARD',
      },
      {
        icon: '',
        uploadControl: false,
        handler: () => Router.push('/dashboard'),
        value: 'MY PROJECTS',
      },
      {
        icon: '',
        uploadControl: false,
        handler: () => {
          Router.push('/callouts');
          mixpanelAnalytics('view_callouts', {
            pageName: 'dashboard',
            redirectUrl: `/callouts`,
          });
        },
        value: 'CALL OUTS',
      },
      // {
      //   icon: '',
      //   uploadControl: false,
      //   handler: () => Router.push('/discovery'),
      //   value: 'DISCOVERY',
      // },
      {
        icon: '',
        uploadControl: false,
        handler: () => Router.push('/marketPlace'),
        value: 'MARKET PLACE',
      },
      {
        icon: '',
        uploadControl: false,
        handler: () => this.handleWathList(),
        value: 'WATCH LIST',
        lockIcon:
          !accessibleFeature.watchList ||
          this.props.subscriptionStatus === 'expired',
      },
      {
        icon: '',
        uploadControl: false,
        handler: () => Router.push('/myaccount'),
        value: 'MY PROFILE',
      },
      {
        icon: '',
        uploadControl: false,
        handler: this.logoutRedirect,
        value: 'SIGN OUT',
      },
    ];
    return (
      <>
        <div
          name="main"
          className={`${style.dashboardHeader} ${style[fixedtop]} w-100 p-0 row m-0 dashboardHeader`}
        >
          {/* <div className="row w-auto"> */}
          <div
            className={`${style.btncontainer} d-flex justify-content-start col m-0 p-0`}
          >
            {btnName && (
              <div className="w-auto">
                <Button
                  className="p-0"
                  name="continue"
                  btntype="submit"
                  size="medium"
                  isActive
                  customClass="--text"
                  clickHandler={clickHandler}
                  buttonValue={
                    <span className={`mt-1 ${style.btnvalue}`}>{btnName}</span>
                  }
                  iconPosition="left"
                  icon={BackIconSvgPath}
                />
              </div>
            )}
          </div>
          <div className="text-center p-0 m-0 col d-flex justify-content-center align-items-center">
            <center>
              <SmashLogo
                src={`/assets/svg/smashLogo.svg`}
                width="133px"
                height="40px"
                noLogoLink={noLogoLink}
                variant={this.props.currentPlan}
                onClick={this.handleNavigation}
              />
            </center>
          </div>
          <div
            className={`justify-content-end d-flex col p-0`}
            tabIndex={-1}
            onBlur={this.hideDropdown}
          >
            {children}
            <div className={`${style.dropdown}`}>
              <>
                <img
                  src={
                    !noAvatar && profileImage
                      ? profileImage
                      : '/assets/jpg/Placeholder_Avatar_320.jpg'
                  }
                  name="facebook"
                  height="48px"
                  width="48px"
                  className={`${style.profile} rounded-circle ${
                    showDropdown === 'visibledropdown'
                      ? 'd-md-block d-lg-block d-sm-none d-none'
                      : ''
                  }`}
                  alt=""
                  onClick={this.showDropdown}
                />

                <div
                  onClick={this.hideDropdown}
                  className={`mt-2 ${
                    showDropdown === 'visibledropdown'
                      ? 'd-sm-block d-md-none'
                      : 'd-none'
                  }`}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M4.99988 17.7279L6.41409 19.1421L12.071 13.4852L17.728 19.1422L19.1422 17.7279L13.4852 12.071L19.142 6.41421L17.7278 5L12.071 10.6568L6.41424 5.00002L5.00003 6.41423L10.6568 12.071L4.99988 17.7279Z"
                      fill="#05012D"
                    />
                  </svg>
                </div>
              </>
            </div>
          </div>
          {/* </div> */}
          <DropDown
            isHeaderDropdown
            options={dropdownOptions}
            dropdownclass={`${style.dropdownPos} ${style[showDropdown]} col col-12`}
          />
        </div>
        {(subscriptionReminder ||
          subscriptionCancelled ||
          this.props.subscriptionStatus === 'expired' ||
          isTrial) && (
          <div className={`${mobileBannerStyle.bannerContainer}`}>
            <SubsCriptionReminder
              subscriptionStatus={this.props.subscriptionStatus}
              currentPlan={this.props.currentPlan}
              isTrial={isTrial}
              getReminder={(value) => {
                // Update banner visibility when reminder is closed
                this.updateBannerVisibility(!value);
                this.props.getReminder(value);
              }}
              handleSubscriptionModal={this.handleSubscriptionModal}
              subscription={subscription}
              isLegacy={get(subscription, 'isLegacy')}
              subscriptionCancelled={subscriptionCancelled}
              setsubscriptionCancelledStatus={
                this.setsubscriptionCancelledStatus
              }
            />
          </div>
        )}
      </>
    );
  }
}

DashboardHeader.defaultProps = {
  profile: PropTypes.shape({
    profileImage: '',
  }),
  children: null,
  fixedtop: '',
};

DashboardHeader.propTypes = {
  profile: PropTypes.shape({
    profileImage: PropTypes.string,
  }),
  fixedtop: PropTypes.string,
  children: PropTypes.object,
  logout: PropTypes.func.isRequired,
  btnName: PropTypes.string,
  clickHandler: PropTypes.func,
  noAvatar: PropTypes.bool,
};

const mapStateToProps = (state) => ({
  subscriptionReminder: state.subscription.subscriptionReminder,
  allPlans: state.subscription.allPlans,
  currentPlan: state.subscription.currentPlan,
  accessibleFeature: state.subscription.accessibleFeature,
  isTrial: state.subscription.isTrial,
  subscriptionStatus: state.subscription.subscriptionStatus,
});

const mapDistpatchToProps = (dispatch) => {
  return {
    logout: (payload) => dispatch(logout(payload)),
    getReminder: () => dispatch(getReminder()),
    setSubscriptionJourneyModal: (payload) =>
      dispatch(setSubscriptionJourneyModal(payload)),
    getPlans: (payload) => dispatch(getPlans(payload)),
    getSubscription: (payload) => dispatch(getSubscription(payload)),
    getManageSubscriptionLink: () => dispatch(getManageSubscriptionLink()),
    getStripeCheckoutLink: (payload) =>
      dispatch(getStripeCheckoutLink(payload)),
  };
};

export default connect(
  mapStateToProps,
  mapDistpatchToProps,
)(withRouter(DashboardHeader));
