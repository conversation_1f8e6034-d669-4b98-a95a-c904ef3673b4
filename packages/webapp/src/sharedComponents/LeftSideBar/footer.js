/* eslint-disable @next/next/no-img-element */
import React from 'react';
import { connect } from 'react-redux';
import { Popover, OverlayTrigger } from 'react-bootstrap';
import { withTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import _ from 'lodash';
import Router, { withRouter } from 'next/router';
import style from '../styles/dashboardHeader.module.scss';
import { setSubscriptionJourneyModal } from 'reducer/subscription';
import LockIcon from 'sharedComponents/Icon/LockIcon';
import mixpanelAnalytics from 'lib/mixpanel';

// footer for leftSideBar
class footer extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      showOverlay: false, // State variable to control overlay visibility
    };
  }

  // Method to toggle visibility
  toggleOverlay = () => {
    this.setState((prevState) => ({ showOverlay: !prevState.showOverlay }));
  };

  // method to logout user from app
  logoutRedirect = () => {
    const { logout } = this.props;
    logout(this.props);
  };

  handleWathList = () => {
    const { setSubscriptionJourneyModal, accessibleFeature } = this.props;
    if (
      accessibleFeature.watchList &&
      this.props.subscriptionStatus !== 'expired'
    ) {
      setSubscriptionJourneyModal({
        modalStatus: false,
        feature: '',
        onComplete: () => {},
      }),
        this.toggleOverlay();
      Router.push('/discoverer/dashboard');
    } else {
      this.toggleOverlay();
      setSubscriptionJourneyModal({
        modalStatus: true,
        onComplete: this.handleWathList,
        feature: 'watchList',
      });
    }
  };

  MyPopover = (dropdownOptions) => {
    return (
      <Popover
        id="popover-basic"
        className="footerPopover"
        style={{
          width: '184px',
          maxWidth: '200px',
        }}
      >
        <Popover.Content>
          <div className="p-8">
            <div style={style}>
              {dropdownOptions.map((item) => (
                <p
                  className={`${style.popoverFooter} justify-content-between d-flex`}
                  key={`id_${item.value}`}
                  onClick={!item.uploadControl ? item.handler : () => {}}
                  data-cy={`${item.value}`}
                >
                  {item.value}
                  {item.lockIcon ? (
                    <div>
                      <LockIcon
                        height={17}
                        width={16}
                        show={true}
                        viewbox="0 0 16 17"
                      />
                    </div>
                  ) : null}
                </p>
              ))}
            </div>
          </div>
        </Popover.Content>
      </Popover>
    );
  };

  render() {
    const { userData, noAvatar, accessibleFeature } = this.props;
    const firstName = _.get(userData, 'profile.name.firstName', '');
    const lastName = _.get(userData, 'profile.name.lastName', '');
    const fullName = `${firstName} ${lastName}`;
    const profile = _.get(userData, 'profile');
    const profileImage = _.get(profile, 'profileImage');
    const dropdownOptions = [
      {
        icon: '',
        uploadControl: false,
        handler: () => Router.push('/mydashboard'),
        value: 'DASHBOARD',
      },
      {
        icon: '',
        uploadControl: false,
        handler: () => Router.push('/dashboard'),
        value: 'MY PROJECTS',
      },
      {
        icon: '',
        uploadControl: false,
        handler: () => {
          Router.push('/callouts');
          mixpanelAnalytics('view_callouts', {
            pageName: 'dashboard',
            redirectUrl: `/callouts`,
          });
        },
        value: 'CALL OUTS',
      },
      {
        icon: '',
        uploadControl: false,
        handler: () => Router.push('/discovery'),
        value: 'DISCOVERY',
      },
      {
        icon: '',
        uploadControl: false,
        handler: () => Router.push('/marketPlace'),
        value: 'MARKET PLACE',
      },
      {
        icon: '',
        uploadControl: false,
        handler: () => this.handleWathList(),
        value: 'WATCH LIST',
        lockIcon:
          !accessibleFeature.watchList ||
          this.props.subscriptionStatus === 'expired',
      },
      {
        icon: '',
        uploadControl: false,
        handler: () => Router.push('/myaccount'),
        value: 'MY PROFILE',
      },
      {
        icon: '',
        uploadControl: false,
        handler: this.logoutRedirect,
        value: 'SIGN OUT',
      },
    ];
    return (
      <div style={{ height: '70px' }} className="mx-3">
        {!noAvatar && (
          // <div className="row">
          //   <div className="ml-4 mt-2">
          //     <button
          //       type="button"
          //       onBlur={this.hideDropdown}
          //       onClick={this.showDropdown}
          //       className={style.btnedt}
          //     >
          //       <img
          //         src={profileImage || '/assets/jpg/Placeholder_Avatar_320.jpg'}
          //         height="48px"
          //         width="48px"
          //         className={`${style.profile} rounded-circle  mr-0 float-right`}
          //         alt=""
          //         onClick={this.showDropdown}
          //         onBlur={this.hideDropdown}
          //         data-cy="footerDropdown"
          //       />
          //     </button>
          //     <DropDown
          //       options={dropdownOptions}
          //       dropdownclass={`${style.dropdownPosLeft} ${style[showDropdown]}`}
          //       style={{ right: 'auto' }}
          //     />
          //   </div>
          //   <div className="mt-4 ml-2 pl-0">
          //     <p style={{ color: '#05012D' }}>{fullName}</p>
          //   </div>
          // </div>

          <OverlayTrigger
            show={this.state.showOverlay}
            trigger="click"
            placement="right"
            overlay={this.MyPopover(dropdownOptions)}
          >
            <button
              type="button"
              className="btnClass"
              onClick={this.toggleOverlay}
            >
              <div className="row">
                <div className="mt-2 mx-3">
                  <div className={style.btnedt}>
                    <img
                      src={
                        profileImage || '/assets/jpg/Placeholder_Avatar_320.jpg'
                      }
                      height="48px"
                      width="48px"
                      className={`${style.profile} rounded-circle  mr-0 float-right`}
                      alt=""
                      data-cy="footerDropdown"
                    />
                  </div>
                </div>
                <div className="mt-4 ml-2 pl-0">
                  <p style={{ color: '#05012D' }}>{fullName}</p>
                </div>
              </div>
            </button>
          </OverlayTrigger>
        )}
      </div>
    );
  }
}
footer.propTypes = {
  logout: PropTypes.func.isRequired,
  noAvatar: PropTypes.bool.isRequired,
  userData: PropTypes.object.isRequired,
};

const mapStateToProps = (state) => ({
  accessibleFeature: state.subscription.accessibleFeature,
  subscriptionStatus: state.subscription.subscriptionStatus,
});

const mapDistpatchToProps = (dispatch) => {
  return {
    setSubscriptionJourneyModal: (payload) =>
      dispatch(setSubscriptionJourneyModal(payload)),
  };
};

export default connect(
  mapStateToProps,
  mapDistpatchToProps,
)(withTranslation('common')(withRouter(footer)));
