/* eslint-disable @next/next/no-img-element */
import React from 'react';
import { withTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { get } from 'lodash';
import Modal from './templateModal';
import GoalsModal from 'sharedComponents/Modal/modal';
import options from 'configuration/options.json';
import Style from '../../pages/project/preview/style/dashboard.module.scss';
import style from '../styles/leftSideBar.module.scss';
import tours from 'configuration/tours.json';
import { setSubscriptionJourneyModal } from 'reducer/subscription';
import InlineSvg from 'sharedComponents/inline-svg';

// colorTheme for projectDashboard
class colorTheme extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      isShowTemplates: false,
      changeTheme: '',
      isOpenModal: false,
      isOpenGoles: false,
      activeGoal: false,
      isOpenCollaboration: false,
    };
  }

  // method to change theme of project
  isShowTemplateSwitcher = (tab) => {
    const { isShowTemplates } = this.state;
    const {
      setLeftSideBarActiveTab,
      isShowTab,
      isSidebarOpen,
      setCollapseSideBarActiveTab,
    } = this.props;

    if (isShowTab !== false) {
      if (isSidebarOpen) {
        setLeftSideBarActiveTab(tab);
      } else {
        setCollapseSideBarActiveTab(tab);
      }
    }

    this.setState({ isShowTemplates: !isShowTemplates, isOpenGoles: false });
  };

  // method to change theme of project
  changeColor = async (value) => {
    const { toggleTheme, updateProjectData, projectPreviewData } = this.props;
    await updateProjectData({ theme: value }, projectPreviewData._id);
    await toggleTheme(value);
  };

  // method to change theme of project
  isChangeTheame = (value) => {
    this.setState({ changeTheme: value });
  };

  // Set Project tab active.
  handleProjectTab = (tab) => {
    const { setLeftSideBarActiveTab, isSidebarOpen } = this.props;
    if (isSidebarOpen) {
      setLeftSideBarActiveTab(tab);
    }

    this.setState({ isShowTemplates: false, isOpenGoles: false });
  };

  // Set Goals tab active.
  handleGoals = (tab) => {
    const { setLeftSideBarActiveTab, projectPreviewData } = this.props;
    const { isOpenGoles } = this.state;
    const sectionsGoal = get(projectPreviewData, 'goals.goal', false);
    setLeftSideBarActiveTab(tab);
    if (!sectionsGoal) {
      this.setState({ isShowTemplates: false, isOpenGoles: !isOpenGoles });
      setLeftSideBarActiveTab('goal-modal');
    } else {
      setLeftSideBarActiveTab('goals-tab');
      this.setState({ isShowTemplates: false });
    }
  };

  // This method used for open collaborator modal.
  showCollaboratorsModal = () => {
    const {
      projectPreviewData,
      fetchCollaboratorList,
      setLeftSideBarActiveTab,
      isSidebarOpen,
      setCollapseSideBarActiveTab,
      setSubscriptionJourneyModal,
      accessibleFeature,
    } = this.props;
    const { isOpenCollaboration } = this.state;

    if (
      accessibleFeature.projectCollaborators &&
      this.props.subscriptionStatus !== 'expired'
    ) {
      setSubscriptionJourneyModal({
        modalStatus: false,
        feature: '',
        onComplete: () => {},
      });
      fetchCollaboratorList(projectPreviewData._id);
      if (isSidebarOpen) {
        setLeftSideBarActiveTab('collaboration');
      } else {
        setCollapseSideBarActiveTab('collaboration');
      }

      this.setState({
        isShowTemplates: false,
        isOpenGoles: false,
        isOpenCollaboration: !isOpenCollaboration,
      });
    } else {
      setSubscriptionJourneyModal({
        modalStatus: true,
        onComplete: this.showCollaboratorsModal,
        feature: 'projectCollaborators',
      });
    }
  };

  saveTheame = () => {
    const { changeTheme } = this.state;
    if (changeTheme !== '') {
      this.changeColor(changeTheme);
    }
    this.setState({ isOpenModal: false });
  };

  openModal = () => {
    this.setState({ isOpenModal: true });
  };

  closeModal = () => {
    this.setState({ isOpenModal: false });
  };

  selectGoals = (/*projects*/) => {
    // const { startTour, setLeftSideBarActiveTab } = this.props;
    // const { activeGoal } = this.state;
    // this.setState({ isOpenGoles: false });
    // startTour(activeGoal);
    // setLeftSideBarActiveTab(projects);
  };

  closeGoalsModal = () => {
    this.setState({ isOpenGoles: false });
  };

  // method to show body of modal
  body = () => {
    const { changeTheme } = this.state;
    const { projectPreviewData } = this.props;
    const theme = get(projectPreviewData, 'theme');
    let chooseTheme = changeTheme !== '' ? changeTheme : theme;
    return (
      <div className="row">
        <div className="col-6 d-flex flex-row">
          <div className="col-9">
            {chooseTheme === 'light' ? (
              <InlineSvg
                src="/assets/svg/lightTheme.svg"
                className={`${style.themeImgPreview}`}
                alt="template"
              />
            ) : (
              <InlineSvg
                src="/assets/svg/darkTheme.svg"
                className={`${style.themeImgPreview}`}
                alt="template"
              />
            )}
          </div>
          <div className="col-3 pt-4 pb-4">
            {options.defaultThemesColors.map((item, index) => (
              <div
                key={String(index)}
                role="button"
                style={{
                  background: `linear-gradient(to top,  ${item.bgColorBottom} 0%, ${item.bgColorBottom} 50%, ${item.bgColorTop} 50%, ${item.bgColorTop} 100%)`,
                }}
                className={`${Style.dotDark} ${
                  chooseTheme === item.themeType ? `${Style.active}` : ''
                }`}
                onClick={() => this.isChangeTheame(item.themeType)}
                aria-label="Color Dark"
              />
            ))}
          </div>
        </div>
        <div className="col-6 d-flex flex-row">
          <div className="col-9">
            {chooseTheme === 'bcsTemplate2' ? (
              <InlineSvg
                src="/assets/svg/bcsTheme2.svg"
                className={`${style.themeImgPreview}`}
                alt="template"
              />
            ) : chooseTheme === 'bcsTemplate3' ? (
              <InlineSvg
                src="/assets/svg/bcsTheme3.svg"
                className={`${style.themeImgPreview}`}
                alt="template"
              />
            ) : (
              <InlineSvg
                src="/assets/svg/bcsTheme.svg"
                className={`${style.themeImgPreview}`}
                alt="template"
              />
            )}
          </div>
          <div className="col-3 pt-4 pb-4">
            {options.bcsThemesColors.map((item, index) => (
              <div
                key={String(index)}
                role="button"
                style={{
                  background: `linear-gradient(to top,  ${item.bgColorBottom} 0%, ${item.bgColorBottom} 50%, ${item.bgColorTop} 50%, ${item.bgColorTop} 100%)`,
                }}
                className={`${Style.dotDark} ${
                  chooseTheme === item.themeType ? `${Style.active}` : ''
                }`}
                onClick={() => this.isChangeTheame(item.themeType)}
                aria-label="Color Dark"
              ></div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // setGoal
  setGoal = (goal) => {
    this.setState({
      activeGoal: goal.id,
    });
  };

  // method to show body of modal
  goalsBody = () => {
    // get the goals
    const goals = tours.scenarios;
    const { activeGoal } = this.state;

    return (
      <div className="row">
        <div className="col-12 m0Auto">
          <p className="">
            Please choose a goal that best describes your objectives for this
            project so we can better support you along the way:
          </p>
        </div>
        <div className="row d-flex justify-content-around">
          {goals &&
            goals.map((item, index) => {
              return (
                <div
                  key={String(index)}
                  className={`col-5 d-flex flex-row ${
                    style.goalsShadow
                  } ml-4 mr-4 mb-3 mt-3 ${
                    activeGoal === item.id ? style.goalsBorder : ''
                  }`}
                  onClick={() => {
                    this.setGoal(item);
                  }}
                  role="button"
                >
                  <div
                    className="col-2 rounded-circle"
                    style={{ height: '46px', backgroundColor: item.background }}
                  />
                  <div className="col-10">
                    <p className="p1 m-0">{item.text}</p>
                  </div>
                </div>
              );
            })}
        </div>
      </div>
    );
  };

  render() {
    const { isOpenModal, isOpenGoles } = this.state;
    const {
      projectPreviewData,
      activeTab,
      isShowTab,
      userData,
      isSidebarOpen,
    } = this.props;
    const userId = get(userData, '_id');
    const createdBy = get(projectPreviewData, 'creator.userId');

    return (
      <>
        <Modal
          modalShow={isOpenModal}
          title="Choose your template"
          body={this.body()}
          closeCallback={() => this.closeModal()}
          isShowCrossBtn
          closeBtnText="CANCEL"
          successCallback={() => this.saveTheame()}
          successBtnText="SAVE"
          successBtnClass="--primaryNavy"
          closeBtnClass="--secondaryChaney"
        />
        <GoalsModal
          modalShow={isOpenGoles}
          title="Choose your project goal"
          body={this.goalsBody()}
          closeCallback={() => this.closeGoalsModal()}
          isShowCrossBtn
          closeBtnText="SKIP"
          successCallback={() => this.selectGoals('projects')}
          successBtnText="CONTINUE"
          closeBtnClass="--secondaryChaney"
          successBtnClass="--primaryNavy"
          modalSize="lg"
        />

        <div
          className={`${isShowTab !== false ? '' : 'pl-0'} ${
            !isSidebarOpen ? 'mt-2 border-bottom' : 'mt-3'
          } d-flex col`}
        >
          {isShowTab !== false && isSidebarOpen && (
            <p
              className={`${style.templateText} mr-18 `}
              style={{ opacity: activeTab !== 'project' ? 0.5 : 1 }}
              onClick={() => this.handleProjectTab('project')}
              role="button"
            >
              {activeTab === 'project' ? <u>Project</u> : 'Project'}
            </p>
          )}
          {isShowTab !== false && (
            <p
              data-cy="colorTitle"
              className={`${style.templateText} mr-18 `}
              style={{ opacity: activeTab !== 'templates' ? 0.5 : 1 }}
              onClick={() => this.isShowTemplateSwitcher('templates')}
              role="button"
            >
              {activeTab === 'templates' ? <u>Templates</u> : 'Templates'}
            </p>
          )}
          {/* {isShowTab !== false && (
              <p
              className={`${style.templateText} p1 mt-1 mr-4 `}
                style={{
                  opacity:
                    activeTab !== 'goals-modal' || activeTab !== 'goals-tab'
                      ? 0.5
                      : 1,
                }}
                onClick={() => this.handleGoals()}
                role="button"
              >
                {activeTab === 'goals-modal' || activeTab === 'goals-tab' ? (
                  <u>Goals</u>
                ) : (
                  'Goals'
                )}
              </p>
            )} */}
          {userId === createdBy && (
            <p
              className={`${style.templateText}`}
              style={{ opacity: activeTab !== 'collaboration' ? 0.5 : 1 }}
              onClick={() => this.showCollaboratorsModal('collaboration')}
              role="button"
            >
              {activeTab === 'collaboration' ? (
                <u>Collaboration</u>
              ) : (
                'Collaboration'
              )}
            </p>
          )}
        </div>
      </>
    );
  }
}
colorTheme.propTypes = {
  projectPreviewData: PropTypes.func.isRequired,
  toggleTheme: PropTypes.func.isRequired,
  updateProjectData: PropTypes.func.isRequired,
  activeTab: PropTypes.object.isRequired,
  setLeftSideBarActiveTab: PropTypes.func.isRequired,
  startTour: PropTypes.func.isRequired,
  isShowTab: PropTypes.bool.isRequired,
  fetchCollaboratorList: PropTypes.func.isRequired,
  userData: PropTypes.object.isRequired,
};

const mapStateToProps = (state) => ({
  accessibleFeature: state.subscription.accessibleFeature,
  subscriptionStatus: state.subscription.subscriptionStatus,
});

const mapDistpatchToProps = (dispatch) => {
  return {
    setSubscriptionJourneyModal: (payload) =>
      dispatch(setSubscriptionJourneyModal(payload)),
  };
};

export default connect(
  mapStateToProps,
  mapDistpatchToProps,
)(withTranslation('common')(colorTheme));
