/* eslint-disable @next/next/no-img-element */
import React from 'react';
import { withTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { get } from 'lodash';
import options from 'configuration/options.json';
import Style from '../../pages/project/preview/style/dashboard.module.scss';
import style from '../styles/leftSideBar.module.scss';
import InlineSvg from 'sharedComponents/inline-svg';
import mixpanelAnalytics from 'lib/mixpanel';

// ShowTemplate for projectDashboard
class ShowTemplate extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      changeTheme: '',
    };
  }

  // method to change theme of project
  changeColor = async (value) => {
    const { toggleTheme, updateProjectData, projectPreviewData } = this.props;
    await updateProjectData({ theme: value }, projectPreviewData._id);
    await toggleTheme(value);
    mixpanelAnalytics('project_theme_selected', {
      pageName: 'project_preview',
      theme: value,
    });
  };

  handleThemeChange = (theme) => {
    const { toggleTheme } = this.props;
    toggleTheme(theme);
  };

  render() {
    const { projectPreviewData, isShowTab } = this.props;
    const theme = get(projectPreviewData, 'theme');

    const { changeTheme } = this.state;
    let chooseTheme = changeTheme !== '' ? changeTheme : theme;
    let buttonRender = [];
    switch (theme) {
      case 'dark':
        buttonRender = options.defaultThemesColors;
        break;
      case 'light':
        buttonRender = options.defaultThemesColors;
        break;
      case 'bcsTemplate':
        buttonRender = options.defaultThemesColors;
        break;
      case 'bcsTemplate2':
        buttonRender = options.defaultThemesColors;
        break;
      case 'bcsTemplate3':
        buttonRender = options.defaultThemesColors;
        break;
      case 'newTemplate':
        buttonRender = options.defaultThemesColors;
        break;
      default:
        buttonRender = options.defaultThemesColors;
    }

    const themeButton = buttonRender.map((item, index) => {
      return (
        <div
          key={String(index)}
          role="button"
          style={{
            background: `linear-gradient(to top,  ${item.bgColorBottom} 0%, ${item.bgColorBottom} 50%, ${item.bgColorTop} 50%, ${item.bgColorTop} 100%)`,
          }}
          className={`${Style.dotDark} ${
            theme === item.themeType ? `${Style.active}` : ''
          }`}
          onClick={() => this.changeColor(item.themeType)}
          aria-label="Color Dark"
          data-cy="darkButton"
        />
      );
    });

    const bcsButton = options.bcsThemesColors.map((item, index) => {
      return (
        <div
          key={String(index)}
          role="button"
          style={{
            background: `linear-gradient(to top,  ${item.bgColorBottom} 0%, ${item.bgColorBottom} 50%, ${item.bgColorTop} 50%, ${item.bgColorTop} 100%)`,
          }}
          className={`${Style.dotDark} ${
            chooseTheme === item.themeType ? `${Style.active}` : ''
          }`}
          onClick={() => this.changeColor(item.themeType)}
          aria-label="Color Dark"
        ></div>
      );
    });

    //new template theme
    const newTemplateButton = options.newDefaultTemplate.map((item, index) => {
      return (
        <div
          key={String(index)}
          role="button"
          style={{
            background: `linear-gradient(to top, ${item.bgColorBottom} 0%, ${item.bgColorBottom} 50%, ${item.bgColorTop} 50%, ${item.bgColorTop} 100%)`,
          }}
          className={`${Style.dotDark} ${
            chooseTheme === 'newTemplate' ? `${Style.active}` : ''
          }`}
          onClick={() => this.changeColor('newTemplate')}
          aria-label="New Template Color"
          data-cy="newTemplateButton"
        ></div>
      );
    });

    return (
      <div className="row mt-3 m-0">
        <div className={`${isShowTab !== false ? '' : 'pl-0'} `}>
          <p className="p1">Choose a template:</p>
        </div>
        <div
          className={`col-12 m0Auto d-flex flex-row mt-3 p-0 ${style.templateHeight}`}
        >
          <div className="col-6 pl-0">
            {chooseTheme === 'newTemplate' ? (
              <InlineSvg
                src="/assets/svg/newDefaultTemplate.svg"
                className={`${style.themeImgPreview}`}
                alt="template"
              />
            ) : (
              <InlineSvg
                src="/assets/svg/newDefaultTemplate.svg"
                className={`${style.themeImgPreview}`}
                alt="template"
              />
            )}
          </div>
          <div className="col-6 p-0 d-lg-flex d-md-block flex-row">
            <div className="col-4 d-flex flex-column p-0">
              {newTemplateButton}
            </div>
          </div>
        </div>
        <div
          className={`col-12 m0Auto d-flex flex-row mt-3 p-0 ${style.templateHeight}`}
        >
          <div className="col-6 pl-0">
            {theme === 'light' ? (
              <InlineSvg
                src="/assets/svg/lightTheme.svg"
                className={`${style.themeImgPreview}`}
                alt="template"
              />
            ) : (
              <InlineSvg
                src="/assets/svg/darkTheme.svg"
                className={`${style.themeImgPreview}`}
                alt="template"
              />
            )}
          </div>
          <div className="col-6 p-0 d-lg-flex d-md-block flex-row">
            <div className="col-4 d-flex flex-column p-0">{themeButton}</div>
          </div>
        </div>
        <div
          className={`col-12 m0Auto d-flex flex-row mt-3 p-0 ${style.templateHeight}`}
        >
          <div className="col-6 pl-0">
            {chooseTheme === 'bcsTemplate2' ? (
              <InlineSvg
                src="/assets/svg/bcsTheme2.svg"
                className={`${style.themeImgPreview}`}
                alt="template"
              />
            ) : chooseTheme === 'bcsTemplate3' ? (
              <InlineSvg
                src="/assets/svg/bcsTheme3.svg"
                className={`${style.themeImgPreview}`}
                alt="template"
              />
            ) : (
              <InlineSvg
                src="/assets/svg/bcsTheme.svg"
                className={`${style.themeImgPreview}`}
                alt="template"
              />
            )}
          </div>
          <div className="col-6 p-0 d-lg-flex d-md-block flex-row">
            <div className="col-4 d-flex flex-column p-0">{bcsButton}</div>
          </div>
        </div>
      </div>
    );
  }
}
ShowTemplate.propTypes = {
  projectPreviewData: PropTypes.func.isRequired,
  toggleTheme: PropTypes.func.isRequired,
  updateProjectData: PropTypes.func.isRequired,
  isShowTab: PropTypes.bool.isRequired,
};

export default withTranslation('common')(ShowTemplate);
