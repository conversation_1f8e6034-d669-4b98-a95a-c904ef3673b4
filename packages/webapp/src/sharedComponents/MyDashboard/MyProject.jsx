import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { get, sortBy } from 'lodash';
import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'next/router';
import Button from 'sharedComponents/Button/button';
import Loader from 'sharedComponents/loader';
import SectionHeader from 'sharedComponents/SectionHeader/sectionHeader';
import CustomCarousel from 'sharedComponents/CustomCaraousel/carousel';
import Icon from 'sharedComponents/Icon/Icon';
import PlusIconSvgPath from 'svgpath/PlushButtonSvgPath';
import LockIcon from 'sharedComponents/Icon/LockIcon';
import { setSubscriptionJourneyModal } from 'reducer/subscription';
import ExistingProject from '../../pages/dashboard/ExisitngProject';
import Overview from '../../pages/dashboard/Overview';
import Style from '../../pages/dashboard/styles/startApp.module.scss';

/**
 * MyProjects - A component to display user's projects with carousel layout
 *
 * @param {Object} props - Component props
 * @returns {JSX.Element} - Rendered component
 */
const MyProjects = ({
  projectsList: propsProjectsList,
  deleteProject,
  collaboratorList: propsCollaboratorList,
  createCollaborator,
  fetchCollaboratorList,
  updateOtherDocuments,
  sendReminderToCollaborator,
  fetchCollaborator,
  deleteCollaborator,
  userData: propsUserData,
  removeSectionItems,
  softDeleteProject,
}) => {
  const router = useRouter();
  const dispatch = useDispatch();

  // Always call hooks in the same order, then decide which data to use
  const reduxProjectsList = useSelector(
    (state) => state.project.projectsList || [],
  );
  const reduxUserData = useSelector((state) => state.auth.userData);
  const reduxCollaboratorList = useSelector(
    (state) => state.project.collaboratorList || [],
  );
  const isLoading = useSelector((state) => state.project.isLoading);
  const accessibleFeature = useSelector(
    (state) => state.subscription.accessibleFeature,
  );
  const subscriptionStatus = useSelector(
    (state) => state.subscription.subscriptionStatus,
  );

  // Use props first, fallback to Redux if props not provided
  const projectsList = propsProjectsList || reduxProjectsList;
  const userData = propsUserData || reduxUserData;
  const collaboratorList = propsCollaboratorList || reduxCollaboratorList;

  const [myProjects, setMyProjects] = useState([]);

  // Calculate hasProjects early to avoid reference errors
  const hasProjects = myProjects.length > 0;

  // Process projects to filter user's own projects
  const processedRef = React.useRef(false);

  useEffect(() => {
    // Reset processed ref when projectsList or userData changes
    processedRef.current = false;
  }, [projectsList, userData]);

  useEffect(() => {
    // Only process if we have both projectsList and userData, and haven't processed yet
    if (projectsList?.length && userData && !processedRef.current) {
      const userId = get(userData, '_id');
      const userEmail = get(userData, 'email');
      const subscriptionType = get(userData, 'userMeta.type');

      // Filter projects that belong to the current user
      let userProjects = projectsList.filter((project) => {
        const creatorId = get(project, 'creator.userId');
        const creatorEmail = get(project, 'creator.email');

        // Check if user is the creator or a collaborator
        const isCreator = creatorId === userId || creatorEmail === userEmail;
        const isCollaborator = project.projectCollaborator?.some(
          (collaborator) => collaborator.email === userEmail,
        );

        return isCreator || isCollaborator;
      });

      // Sort projects same way as Dashboard component
      const list =
        subscriptionType === 'free'
          ? sortBy(userProjects, [(item) => item.createdAt])
          : userProjects || [];

      setMyProjects(list);
      // Mark as processed to prevent unnecessary re-renders
      processedRef.current = true;
    }
  }, [projectsList, userData]);

  const handleViewMore = () => {
    router.push('/dashboard');
  };

  const createProject = () => {
    if (
      (subscriptionStatus !== 'expired' &&
        accessibleFeature.unlimitedProjects) ||
      projectsList.length < 2
    ) {
      dispatch(
        setSubscriptionJourneyModal({
          modalStatus: false,
          feature: '',
          cameFrom: 'dashboard',
          onComplete: () => {},
        }),
      );
      router.push('/project/create');
    } else {
      dispatch(
        setSubscriptionJourneyModal({
          modalStatus: true,
          cameFrom: 'dashboard',
          onComplete: createProject,
          feature: 'unlimitedProjects',
        }),
      );
    }
  };

  const renderCreateProjectCard = () => {
    return (
      <div
        className={`${Style.newAppContainer}`}
        style={{
          height: '400px', // Let carousel control the height
        }}
      >
        {/* Lock Icon */}
        <div className={Style.lockIconContainer}>
          <LockIcon
            onClick={() => createProject()}
            height={24}
            width={24}
            show={
              (!accessibleFeature.unlimitedProjects &&
                projectsList.length >= 2) ||
              (subscriptionStatus === 'expired' && projectsList.length >= 2)
            }
            viewbox="0 0 24 24"
          />
        </div>
        <div className={`${Style.newAppSubContainer}`}>
          <div className={`${Style.plusContainer}`}>
            <div
              data-cy="newProjectContainer"
              className={`${Style.plusBorder}`}
              onClick={() => createProject()}
            >
              <Icon icon={PlusIconSvgPath} color="#00000" iconSize="50px" />
            </div>
          </div>
          <div className={`${Style.existingTitle}`}>
            <p
              className="p1 text-primary text-center mt-32"
              style={{ cursor: 'pointer' }}
              onClick={() => createProject()}
            >
              Create New Project
            </p>
          </div>
        </div>
      </div>
    );
  };

  const renderProjectCard = (item, index) => {
    const userId = get(userData, '_id');
    const userEmail = get(userData, 'email');

    return (
      <div
        className={`${Style.existingAppContainer} p-3 pt-5 p-md-5`}
        key={item._id}
        style={{
          height: '400px', // Let carousel control the height
        }}
      >
        <div>
          <div className={`${Style.existingAppSubContainer}`}>
            <ExistingProject
              item={item}
              index={index}
              userId={userId}
              collaboratorList={collaboratorList}
              accessibleFeature={accessibleFeature}
              subscriptionStatus={subscriptionStatus}
              // Pass the actual Redux action functions
              deleteProject={deleteProject}
              createCollaborator={createCollaborator}
              fetchCollaboratorList={fetchCollaboratorList}
              updateOtherDocuments={updateOtherDocuments}
              sendReminderToCollaborator={sendReminderToCollaborator}
              fetchCollaborator={fetchCollaborator}
              deleteCollaborator={deleteCollaborator}
              removeSectionItems={removeSectionItems}
              softDeleteProject={softDeleteProject}
              setSubscriptionJourneyModal={(payload) =>
                dispatch(setSubscriptionJourneyModal(payload))
              }
            />

            <Overview
              subscriptionStatus={subscriptionStatus}
              projectsList={myProjects}
              cover={item.cover}
              item={item}
              index={index}
              setSubscriptionJourneyModal={(payload) =>
                dispatch(setSubscriptionJourneyModal(payload))
              }
              accessibleFeature={accessibleFeature}
            />
          </div>
        </div>

        {/* Collaborators Display - Same as Dashboard */}
        {item.projectCollaborator && item.projectCollaborator.length > 0 && (
          <div className="d-flex ml-5">
            {item.projectCollaborator.map((collaborator, collabIndex) => {
              return collaborator.email !== userEmail ? (
                <div
                  key={collabIndex}
                  className={`mt-1 ${Style.collaboratorProjectOwnerContainer} d-flex align-items-center`}
                  style={{
                    marginBottom: '10px',
                  }}
                >
                  <div
                    className={`${Style.creatorDp} d-flex align-items-center col m-0 p-0 rounded-circle`}
                  >
                    <img
                      src={
                        get(collaborator, 'profileImage') ||
                        '/assets/jpg/Placeholder_Avatar_320.jpg'
                      }
                      height="40px"
                      width="40px"
                      className="rounded-circle"
                      alt=""
                      style={{ cursor: 'pointer' }}
                    />
                    <div className={`${Style.creatorNameText} p-2`}>
                      <p className="p3 text-primary">
                        {get(collaborator, 'email')}
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <span
                  key={collabIndex}
                  className="badge badge-info rounded-pill"
                >
                  Collaborating
                </span>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  // Only show this section if there are projects or loading, or if it's empty (to show create option)
  if (myProjects.length === 0 && !isLoading) {
    // Show empty state with create project option
    return (
      <div className="row mt-3 mt-md-5 mt-lg-5">
        <div className="col-12">
          <SectionHeader
            title="My Projects"
            showButton={hasProjects}
            buttonText="View All"
            customClass="viewListBtn"
            alignment="left"
            showBackButton={false}
            onButtonClick={handleViewMore}
            headerTitleClass="fs-20"
          />
          <div className="text-center py-4 border-bottom">
            <p className="text-primary mt-4 pb-4">
              No projects yet. Create your first project to get started!
            </p>
            <Button
              btntype="button"
              customClass="primary-button"
              className="px-4 py-2"
              buttonValue="Create New Project"
              clickHandler={createProject}
            />
          </div>
        </div>
      </div>
    );
  }

  // Prepare carousel items - render all items as JSX components
  const carouselItems = [
    renderCreateProjectCard(),
    ...myProjects
      .slice(0, 8)
      .map((project, index) => renderProjectCard(project, index)),
  ];

  return (
    <div className="row mt-3 mt-md-5 mt-lg-5">
      <div className="col-12">
        <SectionHeader
          title="My Projects"
          showButton={hasProjects}
          buttonText="View All"
          customClass="viewListBtn"
          alignment="left"
          showBackButton={false}
          onButtonClick={handleViewMore}
          headerTitleClass="fs-20"
        />

        {isLoading ? (
          <div className="text-center py-4">
            <Loader />
            <p className="mt-3">Loading projects...</p>
          </div>
        ) : (
          <>
            {hasProjects ? (
              <div className="py-3 pt-md-4 pb-5 pt-md-4 border-bottom">
                {/* Carousel Container */}
                <div style={{ minHeight: '400px' }}>
                  <CustomCarousel
                    items={carouselItems}
                    renderItem={(item) => item} // Items are already rendered JSX
                    slidesPerPage={3}
                    autoplay={false}
                  />
                </div>
              </div>
            ) : (
              <p className="text-center text-primary mt-4 border-bottom pb-4">
                No projects here.
              </p>
            )}

            {/* Mobile View More Button */}
            {hasProjects && (
              <div className="d-flex mt-0 mt-md-5 mt-lg-5 d-md-none d-lg-none justify-content-center pb-32 border-bottom">
                <Button
                  btntype="button"
                  customClass="viewListBtn"
                  className="w-100 py-2 px-3"
                  buttonValue="View All"
                  clickHandler={handleViewMore}
                />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

MyProjects.propTypes = {
  projectsList: PropTypes.array,
  deleteProject: PropTypes.func,
  collaboratorList: PropTypes.array,
  createCollaborator: PropTypes.func,
  fetchCollaboratorList: PropTypes.func,
  updateOtherDocuments: PropTypes.func,
  sendReminderToCollaborator: PropTypes.func,
  fetchCollaborator: PropTypes.func,
  deleteCollaborator: PropTypes.func,
  userData: PropTypes.object,
  removeSectionItems: PropTypes.func,
  softDeleteProject: PropTypes.func,
};

export default MyProjects;
