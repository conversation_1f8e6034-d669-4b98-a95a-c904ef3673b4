import axios from 'axios';
import { get, camelCase } from 'lodash';
import { snapFeedback } from 'reducer/project';
import { generateToken } from 'utils/helper';
// import { toast } from 'react-toastify';

/**
 * Function to enable loader
 *
 * @type {boolean} status
 */
export function setLoadingStatus(boolVal) {
  return {
    type: 'SET_LOADING',
    payload: boolVal,
  };
}

/**
 * Function to enable loader
 *
 * @type {boolean} status
 */
export function setCallOutList(data) {
  return {
    type: 'SET_CALLOUT_LIST',
    payload: data,
  };
}

export function setCallOutDetails(data) {
  return {
    type: 'SET_CALLOUT_DETAILS',
    payload: data,
  };
}

/**
 * Function to set callout submissions
 *
 * @type {array} data
 */
export function setCalloutSubmissions(data) {
  return {
    type: 'SET_CALLOUT_SUBMISSIONS',
    payload: data,
  };
}

/**
 * Function to enable loader
 *
 * @type {boolean} status
 */
export function setMyCallOutList(data) {
  return {
    type: 'SET_MY_CALLOUT_LIST',
    payload: data,
  };
}

export function setCallOutData(data) {
  return {
    type: 'SET_CALLOUT_DATA',
    payload: data,
  };
}

export function setAwaitingSlateStatus(data) {
  return {
    type: 'SET_AWAITING_SLATE_DATA',
    payload: data,
  };
}

export function setNotIntrestedSlateStatus(data) {
  return {
    type: 'SET_NOT_INTRESTED_SLATE_DATA',
    payload: data,
  };
}

export function setOtherSlateStatus(data) {
  return {
    type: 'SET_OTHER_SLATE_DATA',
    payload: data,
  };
}

/**
 *
 * @param {*} snapData
 * @returns
 */
export function setCurrentCallout(currentCallout) {
  return {
    type: 'SET_CURRENT_CALLOUT',
    payload: currentCallout,
  };
}

export const getCallOutDetails = (params) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/callout/${params}`,

    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'get',
    responseType: 'json',
  })
    .then((response) => {
      const callOutList = get(response, 'data.data', []);
      dispatch(setCallOutDetails(callOutList));
      return callOutList;
    })
    .catch(() => {
      return {};
    });
};

/**
 * Get all the call outs list that have been created by the user.
 *
 * @returns {*} object
 */
export const getCallOutList = (params) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/callout?$limit=${process.env.ListApiLimit}&$sort=updatedAt|-1`,

    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'get',
    params,
    responseType: 'json',
  })
    .then((response) => {
      const callOutList = get(response, 'data.data.docs', []);
      dispatch(setCallOutList(callOutList));
      return callOutList;
    })
    .catch(() => {
      return {};
    });
};

/**
 * Get all the call outs list that have been created by the user.
 *
 * @returns {*} object
 */

export const getPublicCallOutList = () => async (dispatch) => {
  const token = await generateToken(); // Generate the token
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/callout/public?$limit=${process.env.ListApiLimit}&$sort=updatedAt|-1`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: token,
    },
    method: 'get',
    responseType: 'json',
  })
    .then((response) => {
      const callOutList = get(response, 'data.data.docs', []);
      dispatch(setCallOutList(callOutList));
      return callOutList;
    })
    .catch(() => {
      return {};
    });
};

/**
 * Get all the call outs list that have been created by the user.
 *
 * @returns {*} object
 */
export const getMyCallOutList = () => (dispatch, getState) => {
  const loggedUserId = getState().auth.userData._id;
  const params = {
    'discoverer.id': loggedUserId,
  };

  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/callout?$limit=${process.env.ListApiLimit}&$sort=updatedAt|-1`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'get',
    params,
    responseType: 'json',
  })
    .then((response) => {
      const callOutList = get(response, 'data.data.docs', []);
      dispatch(setMyCallOutList(callOutList));
      return callOutList;
    })
    .catch(() => {
      return {};
    });
};

/**
 * Get all the call outs list that have been created by the user.
 *
 * @returns {*} object
 */
export const getCallOut = (id) => (dispatch) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/callout/${id}`,

    headers: {
      'Content-Type': 'application/json',
    },
    method: 'get',
    responseType: 'json',
  })
    .then((response) => {
      const callOut = get(response, 'data.data', {});
      dispatch(setCallOutData(callOut));
      const slates = get(callOut, 'slates', []);
      const newSlates = [];
      const notIntrestedSlates = [];
      const otherSlates = [];
      for (const item of slates) {
        if (item.status === 'awaiting_feedback') {
          newSlates.push(item);
        } else if (item.status === 'not_interested') {
          notIntrestedSlates.push(item);
        } else {
          otherSlates.push(item);
        }
      }
      dispatch(setAwaitingSlateStatus(newSlates));
      dispatch(setNotIntrestedSlateStatus(notIntrestedSlates));
      dispatch(setOtherSlateStatus(otherSlates));
      return callOut;
    })
    .catch(() => {
      return {};
    });
};

/**import { get, camelCase } from 'lodash';

 * Get all the call outs list that have been created by the user.
 *
 * @param {object} data
 * @returns {*} object
 */
export const createCallOut = (data) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/callout`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'post',
    responseType: 'json',
    data,
  })
    .then((response) => {
      const data = get(response, 'data.data');
      return { data: { id: get(data, '_id'), ...data } };
    })
    .catch(() => {
      return {};
    });
};

/**
 * Update callout and related data
 *
 * @param {string} id - Callout ID
 * @param {object} data - Updated callout data
 */
export const addSubmissionToCallout = (id, data) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/callout/${id}/submission`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'patch',
    responseType: 'json',
    data,
  })
    .then((response) => {
      const updatedCallout = get(response, 'data.data', {});
      return { data: { id, ...updatedCallout } };
    })
    .catch(() => {});
};

/**
 * Update callout and related data
 *
 * @param {string} id - Callout ID
 * @param {object} data - Updated callout data
 */
export const updateSlateStatus = (id, data) => (dispatch, getState) => {
  return axios({
    url: `${process.env.SmashApiBaseUrl}/v1/callout/${id}/slateStatus`,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'patch',
    responseType: 'json',
    data,
  })
    .then((response) => {
      const updatedCallout = get(response, 'data.data', {});
      dispatch(snapFeedback(camelCase(data.status)));
      return updatedCallout;
    })
    .catch((error) => {
      throw new Error(
        get(error, 'response.data.message', 'Something went wrong'),
      );
    });
};

/**
 * Helper function to fetch a single page of submissions
 *
 * @param {Object} options - Options for the API call
 * @param {number} pageNumber - Page number to fetch
 * @param {Function} getState - Redux getState function
 * @returns {Promise} Promise with submissions data for a single page
 */
const fetchSubmissionsPage = async (options, pageNumber, getState) => {
  const {
    userId = get(getState(), 'auth.userData._id', ''),
    action = 'both',
    limit = process.env.ListApiLimit || 100,
    sort = 'updatedAt|-1',
  } = options;

  // Calculate skip based on page number
  const skip = (pageNumber - 1) * limit;

  // Use the exact URL format that works, but don't limit the fields with $select
  let url = `${process.env.SmashApiBaseUrl}/v1/callout/submissions?$limit=${limit}&$skip=${skip}`;

  // Add sort parameter in the format that works
  if (sort) {
    // Convert sort format from 'field|-1' to 'field|1'
    const sortParts = sort.split('|');
    const sortField = sortParts[0];
    const sortDirection = sortParts.length > 1 ? sortParts[1] : '1';
    url += `&$sort=${sortField}%7C${sortDirection}`;
  } else {
    // Default sort
    url += `&$sort=_id%7C1`;
  }

  // Add action parameter
  if (action) {
    url += `&action=${action}`;
  }

  // Only add userId if it exists
  if (userId) {
    url += `&userId=${userId}`;
  }

  // No logging needed

  const response = await axios({
    url,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getState().auth.token}`,
    },
    method: 'get',
    responseType: 'json',
  });

  return response.data.data;
};

/**
 * Get all submissions for callouts created by the user
 * This function fetches all pages of submissions to get the complete list
 *
 * @param {Object} options - Options for the API call
 * @param {string} options.userId - Optional user ID to filter submissions
 * @param {string} options.action - Optional action type (both, submission, slate)
 * @param {number} options.limit - Optional limit for the number of results per page
 * @param {string} options.sort - Optional sort field and direction
 * @returns {Promise} Promise with all submissions data
 */
export const getCalloutSubmissions =
  (options = {}) =>
  async (dispatch, getState) => {
    try {
      // Use the existing setLoadingStatus function with SET_LOADING type
      dispatch(setLoadingStatus(true));

      // Fetch first page to get total count
      const firstPageResponse = await fetchSubmissionsPage(
        options,
        1,
        getState,
      );

      // If not paginated or no docs, return the response as is
      if (!firstPageResponse || !firstPageResponse.docs) {
        dispatch(setCalloutSubmissions(firstPageResponse || []));
        dispatch(setLoadingStatus(false));
        return firstPageResponse || [];
      }

      // Extract data from first page
      const { total, limit, docs: firstPageDocs } = firstPageResponse;

      // Calculate total pages
      const totalPages = Math.ceil(total / limit);

      // If only one page, return the docs from first page
      if (totalPages <= 1) {
        dispatch(setCalloutSubmissions(firstPageDocs));
        dispatch(setLoadingStatus(false));
        return firstPageDocs;
      }

      // Initialize allDocs with first page results
      let allDocs = [...firstPageDocs];

      // Fetch remaining pages
      const remainingPagesPromises = [];
      for (let page = 2; page <= totalPages; page++) {
        remainingPagesPromises.push(
          fetchSubmissionsPage(options, page, getState),
        );
      }

      // Wait for all remaining pages to be fetched
      const remainingPagesResponses = await Promise.all(remainingPagesPromises);

      // Combine all docs from all pages
      remainingPagesResponses.forEach((pageResponse) => {
        if (
          pageResponse &&
          pageResponse.docs &&
          Array.isArray(pageResponse.docs)
        ) {
          allDocs = [...allDocs, ...pageResponse.docs];
        }
      });

      // All pages fetched successfully

      // Create a complete paginated response with all docs
      const completeResponse = {
        ...firstPageResponse,
        docs: allDocs,
        allPagesLoaded: true,
      };

      // Dispatch the complete response
      dispatch(setCalloutSubmissions(completeResponse));
      dispatch(setLoadingStatus(false));

      return completeResponse;
    } catch (error) {
      // Silent error handling

      // Dispatch empty submissions to avoid UI errors
      dispatch(setCalloutSubmissions([]));
      dispatch(setLoadingStatus(false));
      return [];
    }
  };

/* initialize state and auth reducer */
export const initialState = {
  callOutList: [],
  myCallOutList: [],
  isLoading: false,
  callOutData: {},
  awatingSlateStatus: {},
  notIntrestedSlateStatus: {},
  otherSlateStatus: {},
  currentCallout: null,
  calloutDetails: null,
  calloutSubmissions: [],
};

/**
 * To set ops object in redux
 *
 * @param {any} state - Redux state
 * @param {any} action - Redux action
 * @returns {any} This function return redux auth state.
 */
const callout = (state = initialState, action) => {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'SET_CALLOUT_LIST':
      return {
        ...state,
        callOutList: action.payload,
      };
    case 'SET_MY_CALLOUT_LIST':
      return {
        ...state,
        myCallOutList: action.payload,
      };

    case 'SET_CALLOUT_DATA':
      return {
        ...state,
        callOutData: action.payload,
      };
    case 'SET_AWAITING_SLATE_DATA':
      return {
        ...state,
        awatingSlateStatus: action.payload,
      };
    case 'SET_NOT_INTRESTED_SLATE_DATA':
      return {
        ...state,
        notIntrestedSlateStatus: action.payload,
      };
    case 'SET_OTHER_SLATE_DATA':
      return {
        ...state,
        otherSlateStatus: action.payload,
      };
    case 'SET_CURRENT_CALLOUT':
      return {
        ...state,
        currentCallout: action.payload,
      };
    case 'SET_CALLOUT_DETAILS':
      return {
        ...state,
        calloutDetails: action.payload,
      };
    case 'SET_CALLOUT_SUBMISSIONS':
      return {
        ...state,
        calloutSubmissions: action.payload,
      };
    default:
      return state;
  }
};

export default callout;
